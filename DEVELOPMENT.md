# 开发文档

## 项目结构

```
medical-record-evaluator/
├── public/                 # 静态资源
│   ├── index.html         # HTML模板
│   └── favicon.ico        # 网站图标
├── src/                   # 源代码目录
│   ├── components/        # 可复用组件
│   │   ├── Layout.jsx     # 主布局组件
│   │   ├── ProtectedRoute.jsx  # 路由保护组件
│   │   ├── EvaluationResult.jsx # 评估结果展示
│   │   └── CustomSelect.jsx     # 自定义选择器
│   ├── contexts/          # React Context
│   │   └── AuthContext.jsx      # 认证上下文
│   ├── pages/             # 页面组件
│   │   ├── Login.jsx      # 登录页面
│   │   ├── Dashboard.jsx  # 主页面
│   │   ├── Evaluator.jsx  # 评估页面
│   │   ├── History.jsx    # 历史记录页面
│   │   ├── Settings.jsx   # 设置页面
│   │   └── UserManagement.jsx # 用户管理页面
│   ├── services/          # 业务逻辑服务
│   │   ├── authService.js # 认证服务
│   │   └── llmService.js  # LLM API服务
│   ├── utils/             # 工具函数
│   ├── App.jsx            # 主应用组件
│   ├── main.jsx           # 应用入口
│   └── index.css          # 全局样式
├── package.json           # 项目配置
├── vite.config.js         # Vite配置
├── tailwind.config.js     # Tailwind配置
├── README.md              # 项目说明
└── DEVELOPMENT.md         # 开发文档
```

## 开发命令

```bash
# 开发环境
npm run dev          # 启动开发服务器
npm run build        # 生产构建
npm run preview      # 预览构建结果

# 代码质量
npm run lint         # ESLint代码检查
npm run format       # Prettier代码格式化

# 依赖管理
npm install          # 安装依赖
npm update           # 更新依赖
npm audit            # 安全审计
```

## 技术架构

### 前端架构
- **React 18**: 使用函数组件和Hooks
- **Vite**: 快速的构建工具和开发服务器
- **Tailwind CSS**: 原子化CSS框架
- **React Router**: 客户端路由管理

### 状态管理
- **Context API**: 全局状态管理（认证、主题等）
- **React Hook Form**: 表单状态管理
- **Local Storage**: 本地数据持久化

### 组件设计原则
- **组件化**: 可复用的UI组件
- **响应式**: 适配不同屏幕尺寸
- **可访问性**: 遵循WCAG指南
- **性能优化**: 懒加载和代码分割

## 开发规范

### 代码风格
- 使用ES6+语法
- 函数组件优于类组件
- 使用TypeScript类型注释（可选）
- 遵循ESLint和Prettier规则

### 文件命名
- 组件文件使用PascalCase: `UserProfile.jsx`
- 工具函数使用camelCase: `formatDate.js`
- 常量使用UPPER_SNAKE_CASE: `API_ENDPOINTS.js`

### Git提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建工具或辅助工具的变动
```

## 部署说明

### 生产构建
```bash
npm run build
```

### 静态部署
构建后的文件在`dist/`目录，可以部署到：
- Nginx
- Apache
- Vercel
- Netlify
- GitHub Pages

### 环境变量
```bash
# .env.local
VITE_API_BASE_URL=https://api.example.com
VITE_APP_VERSION=1.0.0
```

## 贡献指南

1. Fork项目
2. 创建功能分支: `git checkout -b feature/new-feature`
3. 提交更改: `git commit -m 'Add new feature'`
4. 推送分支: `git push origin feature/new-feature`
5. 创建Pull Request

## 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件
