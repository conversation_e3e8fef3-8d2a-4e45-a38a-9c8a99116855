{"name": "medical-record-evaluator", "version": "1.0.0", "type": "module", "description": "AI-powered medical record evaluation assistant for healthcare professionals", "main": "index.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "lint": "eslint . --ext js,jsx,ts,tsx", "format": "prettier --write ."}, "keywords": ["medical", "healthcare", "ai", "evaluation", "llm"], "author": "Medical Team", "license": "MIT", "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "axios": "^1.3.0", "lucide-react": "^0.263.1", "recharts": "^2.5.0", "date-fns": "^2.29.3", "react-hook-form": "^7.43.0", "react-hot-toast": "^2.4.0", "clsx": "^1.2.1", "tailwind-merge": "^1.10.0"}, "devDependencies": {"@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@vitejs/plugin-react": "^3.1.0", "vite": "^4.1.0", "tailwindcss": "^3.2.7", "autoprefixer": "^10.4.14", "postcss": "^8.4.21", "eslint": "^8.36.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^2.8.4", "vitest": "^0.29.2", "@testing-library/react": "^14.0.0", "@testing-library/jest-dom": "^5.16.5"}}