# 医疗病历评估系统 📋

<div align="center">

![Version](https://img.shields.io/badge/version-1.0.0-blue.svg)
![React](https://img.shields.io/badge/React-18.x-61dafb.svg)
![Tailwind](https://img.shields.io/badge/Tailwind-3.x-38bdf8.svg)
![License](https://img.shields.io/badge/license-MIT-green.svg)

**基于人工智能的智能化病历质量评估平台**

为医疗机构提供专业的病历评估服务，帮助提升医疗服务质量和效率

[在线演示](http://localhost:3000) · [使用文档](#功能使用教程) · [技术支持](#技术支持)

</div>

---

## 📋 产品概述

### 系统简介

医疗病历评估系统是一个现代化的智能病历质量评估平台，采用先进的大型语言模型技术，为医疗机构提供专业、准确、高效的病历评估服务。系统支持多维度评估、智能分析和个性化建议，是医疗质量管理的理想工具。

### 🚀 主要功能特性

#### 核心评估功能
- **🤖 AI智能评估**：基于大型语言模型的专业病历质量评估
- **📊 多维度评分**：完整性、准确性、规范性、逻辑性四个维度全面评估
- **👨‍⚕️ 专家系统**：内置综合评估、临床专家、文档规范等多种评估模板
- **📝 详细报告**：提供评估总结、改进建议和支持性摘录
- **🎯 自定义评估**：支持自定义评估标准和提示词

#### 数据管理功能
- **📚 评估历史**：完整的评估记录管理和历史查询
- **🔍 智能搜索**：支持按患者、医生、科室、评分等多维度搜索
- **📤 数据导出**：支持JSON、CSV等格式的数据导出
- **🗂️ 分类管理**：按评估类型、时间、评分等分类管理

#### 用户权限管理
- **👥 多角色支持**：管理员、医生、护士等不同角色权限
- **🔐 安全认证**：完整的用户认证和权限控制系统
- **👤 用户管理**：用户信息管理、权限分配、密码重置等
- **📋 操作日志**：详细的用户操作记录和审计功能

#### 技术特性
- **🌐 多LLM支持**：支持OpenAI、Google Gemini、Azure OpenAI等
- **📱 响应式设计**：完美适配桌面端、平板和移动端设备
- **⚡ 高性能**：基于React 18和Vite的现代化前端架构
- **🎨 现代UI**：采用Tailwind CSS的美观现代界面设计

### 🛠 技术栈

<table>
<tr>
<td><strong>分类</strong></td>
<td><strong>技术</strong></td>
<td><strong>版本</strong></td>
<td><strong>用途</strong></td>
</tr>
<tr>
<td rowspan="2">前端框架</td>
<td>React</td>
<td>18.x</td>
<td>用户界面构建</td>
</tr>
<tr>
<td>Vite</td>
<td>4.x</td>
<td>构建工具和开发服务器</td>
</tr>
<tr>
<td rowspan="2">样式系统</td>
<td>Tailwind CSS</td>
<td>3.x</td>
<td>原子化CSS框架</td>
</tr>
<tr>
<td>Lucide React</td>
<td>latest</td>
<td>现代化图标库</td>
</tr>
<tr>
<td rowspan="3">状态管理</td>
<td>React Router</td>
<td>6.x</td>
<td>路由管理</td>
</tr>
<tr>
<td>React Hook Form</td>
<td>7.x</td>
<td>表单状态管理</td>
</tr>
<tr>
<td>Context API</td>
<td>-</td>
<td>全局状态管理</td>
</tr>
<tr>
<td rowspan="3">数据可视化</td>
<td>Recharts</td>
<td>2.x</td>
<td>图表组件库</td>
</tr>
<tr>
<td>React Hot Toast</td>
<td>latest</td>
<td>消息提示组件</td>
</tr>
<tr>
<td>Axios</td>
<td>latest</td>
<td>HTTP请求库</td>
</tr>
</table>

### 👥 目标用户群体

- **🏥 医疗机构**：医院、诊所、社区卫生服务中心等医疗服务提供者
- **👨‍⚕️ 医务人员**：主治医生、住院医师、护士长、护士等一线医务工作者
- **📊 医疗管理者**：医疗质量管理部门、医务科、护理部等管理人员
- **🎓 医学教育**：医学院校、住院医师规范化培训基地、继续教育机构
- **🔍 质控专家**：医疗质量控制中心、第三方评估机构等专业质控人员

---

## 🚀 本地运行指南

### 系统要求

| 组件 | 最低版本 | 推荐版本 | 说明 |
|------|----------|----------|------|
| **Node.js** | 16.0+ | 18.0+ | JavaScript运行环境 |
| **npm** | 7.0+ | 9.0+ | 包管理器 |
| **浏览器** | - | - | Chrome 90+, Firefox 88+, Safari 14+ |
| **内存** | 4GB | 8GB+ | 开发环境推荐配置 |

### 详细安装步骤

#### 1. 获取项目代码
```bash
# 克隆项目仓库
git clone <repository-url>
cd medical-record-evaluator

# 或者下载ZIP包并解压
```

#### 2. 安装项目依赖
```bash
# 使用npm安装
npm install

# 或使用yarn安装（推荐）
yarn install

# 或使用pnpm安装
pnpm install
```

#### 3. 启动开发服务器
```bash
# 启动开发服务器
npm run dev

# 服务器启动后会显示访问地址
# ➜  Local:   http://localhost:3000/
# ➜  Network: use --host to expose
```

#### 4. 访问应用
- 打开浏览器访问 `http://localhost:3000`
- 如果3000端口被占用，Vite会自动尝试其他端口

### 构建和部署

#### 开发环境命令
```bash
npm run dev          # 启动开发服务器
npm run build        # 构建生产版本
npm run preview      # 预览生产构建
npm run lint         # 代码质量检查
```

#### 生产环境构建
```bash
# 构建生产版本
npm run build

# 构建文件将生成在 dist/ 目录
# 可以直接部署到静态文件服务器
```

### 常见问题排查

#### ❌ 端口占用问题
```bash
# 查看端口占用情况
lsof -i :3000

# 或指定其他端口启动
npm run dev -- --port 3001
```

#### ❌ 依赖安装失败
```bash
# 清除npm缓存
npm cache clean --force

# 删除node_modules重新安装
rm -rf node_modules package-lock.json
npm install
```

#### ❌ 构建失败
```bash
# 检查Node.js版本
node --version
npm --version

# 确保版本符合要求
nvm install 18
nvm use 18
```

#### ❌ 内存不足
```bash
# 增加Node.js内存限制
export NODE_OPTIONS="--max-old-space-size=4096"
npm run build
```

---

## 📖 功能使用教程

### 用户登录系统

系统提供完整的用户认证和权限管理功能：

#### 预设测试账户

| 角色 | 用户名 | 密码 | 权限说明 |
|------|--------|------|----------|
| **管理员** | `admin` | `admin123` | 全部功能权限，包括用户管理、系统设置 |
| **医生** | `doctor` | `doctor123` | 病历评估、历史查看、个人设置 |
| **护士** | `nurse` | `nurse123` | 查看评估结果、历史记录 |

#### 登录操作流程

1. **访问登录页面**
   - 打开系统首页，自动跳转到登录页面
   - 或直接访问 `/login` 路径

2. **输入登录凭据**
   ```
   用户名：admin（或其他账户）
   密码：admin123（对应密码）
   ```

3. **身份验证**
   - 点击"登录"按钮
   - 系统验证用户凭据
   - 成功后自动跳转到主页面

4. **权限验证**
   - 系统根据用户角色分配相应权限
   - 不同角色看到不同的菜单和功能

### 病历评估完整操作流程

#### 第一步：进入评估页面
- 从主页点击"开始评估"按钮
- 或点击导航栏"病历评估"菜单

#### 第二步：填写患者信息
```
必填信息：
├── 患者姓名：输入患者真实姓名或匿名标识
├── 病历号：输入医院病历系统编号
├── 科室：选择或输入所属科室
└── 医生：选择或输入主治医生

可选信息：
├── 入院日期：选择患者入院时间
├── 出院日期：选择患者出院时间
└── 诊断：初步诊断信息
```

#### 第三步：选择评审专家
系统提供四种评估专家模式：

1. **🔍 综合评估专家**（推荐）
   - 全面评估病历质量
   - 涵盖完整性、准确性、规范性、逻辑性四个维度
   - 适用于常规病历质量评估

2. **🏥 临床专家**
   - 重点评估临床决策质量
   - 关注诊断准确性、治疗合理性、检查必要性
   - 适用于临床质量专项评估

3. **📝 文档规范专家**
   - 专注于病历书写规范
   - 评估格式规范、用词准确、逻辑结构
   - 适用于文档质量专项评估

4. **⚙️ 自定义专家**
   - 使用自定义评估标准
   - 可以输入特定的评估要求
   - 适用于特殊评估需求

#### 第四步：输入病历内容
- 在大文本框中输入完整病历内容
- 支持复制粘贴操作
- 建议内容长度：100-5000字符
- 系统会自动保存草稿

#### 第五步：开始评估
- 点击"开始评估"按钮
- 系统调用AI模型进行分析
- 等待评估完成（通常需要10-30秒）

#### 第六步：查看评估结果
评估结果包含以下部分：

1. **📊 总体评分**
   - 0-100分制评分
   - 优秀(90+)、良好(80-89)、一般(70-79)、需改进(<70)

2. **📈 分项评分**
   - 完整性评分及说明
   - 准确性评分及说明
   - 规范性评分及说明
   - 逻辑性评分及说明

3. **📝 评估总结**
   - AI生成的专业评估总结
   - 病历整体质量分析

4. **💡 改进建议**
   - 具体的改进建议列表
   - 针对性的优化方案

5. **📋 支持性摘录**
   - 关键内容摘录
   - 评分依据说明

### LLM配置设置

#### 进入设置页面
1. 点击导航栏"系统设置"菜单
2. 选择"LLM配置"选项卡
3. 根据使用的AI服务商进行配置

#### OpenAI配置步骤
1. **选择端点**：选择"OpenAI API"或手动输入
   ```
   https://api.openai.com/v1/chat/completions
   ```

2. **获取API密钥**：
   - 访问 [OpenAI Platform](https://platform.openai.com/api-keys)
   - 登录账户并创建新的API密钥
   - 复制生成的密钥（sk-开头）

3. **配置参数**：
   ```
   API密钥：sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
   模型名称：gpt-4（推荐）或 gpt-3.5-turbo
   Temperature：0.3（推荐值）
   最大令牌：2000
   ```

4. **测试连接**：点击"连接测试"验证配置

#### Google Gemini配置步骤
1. **选择端点**：选择"Google Gemini"
   ```
   https://generativelanguage.googleapis.com/v1/models/gemini-pro:generateContent
   ```

2. **获取API密钥**：
   - 访问 [Google AI Studio](https://makersuite.google.com/app/apikey)
   - 使用Google账户登录
   - 创建新的API密钥

3. **配置参数**：
   ```
   API密钥：AIzaSyxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
   模型名称：gemini-pro
   Temperature：0.3
   ```

#### Azure OpenAI配置步骤
1. **准备Azure资源**：
   - 在Azure Portal创建OpenAI资源
   - 部署模型（如gpt-4）
   - 获取端点URL和API密钥

2. **配置端点**：
   ```
   https://your-resource.openai.azure.com/openai/deployments/your-deployment/chat/completions?api-version=2023-05-15
   ```

3. **填写参数**：
   ```
   API密钥：Azure资源的API密钥
   模型名称：部署的模型名称
   ```

### 评估历史查看和管理

#### 访问历史记录
1. 点击导航栏"评估历史"菜单
2. 查看所有历史评估记录
3. 支持分页浏览和快速搜索

#### 搜索和筛选功能
```
搜索条件：
├── 患者姓名：按患者姓名搜索
├── 病历号：按病历号精确查找
├── 医生姓名：按主治医生搜索
├── 科室：按科室筛选
├── 评分范围：按评分区间筛选
└── 时间范围：按评估时间筛选
```

#### 历史记录操作
- **📋 查看详情**：点击记录查看完整评估结果
- **🔄 重新评估**：基于历史病历重新评估
- **📤 导出记录**：导出单条或批量记录
- **🗑️ 删除记录**：删除不需要的评估记录
- **📊 统计分析**：查看评估趋势和统计数据

### 用户管理功能（管理员权限）

#### 用户列表管理
管理员可以查看和管理所有用户：

1. **查看用户列表**
   - 显示所有注册用户信息
   - 包括用户名、角色、部门、状态等

2. **用户信息编辑**
   ```
   可编辑信息：
   ├── 用户姓名
   ├── 用户角色（管理员/医生/护士）
   ├── 所属部门
   ├── 联系方式
   └── 账户状态（启用/禁用）
   ```

3. **权限管理**
   - 分配用户角色和权限
   - 设置功能访问控制
   - 管理数据访问范围

4. **账户操作**
   - 重置用户密码
   - 停用/启用用户账户
   - 删除用户账户

---

## 🔧 API配置说明

### 支持的LLM服务商

<table>
<tr>
<th>服务商</th>
<th>端点格式</th>
<th>认证方式</th>
<th>推荐模型</th>
</tr>
<tr>
<td><strong>OpenAI</strong></td>
<td><code>https://api.openai.com/v1/chat/completions</code></td>
<td>Bearer Token</td>
<td>gpt-4, gpt-3.5-turbo</td>
</tr>
<tr>
<td><strong>Google Gemini</strong></td>
<td><code>https://generativelanguage.googleapis.com/v1/models/gemini-pro:generateContent</code></td>
<td>API Key (Query)</td>
<td>gemini-pro, gemini-pro-vision</td>
</tr>
<tr>
<td><strong>Azure OpenAI</strong></td>
<td><code>https://{resource}.openai.azure.com/openai/deployments/{deployment}/chat/completions</code></td>
<td>Bearer Token</td>
<td>gpt-4, gpt-35-turbo</td>
</tr>
<tr>
<td><strong>本地部署</strong></td>
<td><code>http://localhost:8000/v1/chat/completions</code></td>
<td>Bearer Token</td>
<td>兼容OpenAI API的模型</td>
</tr>
</table>

### API密钥获取和配置步骤

#### 🔑 OpenAI API密钥获取
1. **注册账户**
   - 访问 [OpenAI Platform](https://platform.openai.com)
   - 使用邮箱注册或登录现有账户

2. **创建API密钥**
   - 进入 [API Keys](https://platform.openai.com/api-keys) 页面
   - 点击"Create new secret key"
   - 为密钥设置名称（如：Medical Evaluator）
   - 复制生成的密钥（以sk-开头）

3. **充值账户**
   - 进入 [Billing](https://platform.openai.com/account/billing) 页面
   - 添加付款方式并充值
   - 设置使用限额

#### 🔑 Google Gemini API密钥获取
1. **访问Google AI Studio**
   - 打开 [Google AI Studio](https://makersuite.google.com/app/apikey)
   - 使用Google账户登录

2. **创建API密钥**
   - 点击"Create API Key"按钮
   - 选择Google Cloud项目（或创建新项目）
   - 复制生成的API密钥

3. **启用API服务**
   - 确保Generative Language API已启用
   - 检查配额和计费设置

#### 🔑 Azure OpenAI配置
1. **创建Azure资源**
   - 登录 [Azure Portal](https://portal.azure.com)
   - 创建"Azure OpenAI"资源
   - 选择合适的区域和定价层

2. **部署模型**
   - 在资源中部署所需模型（如gpt-4）
   - 记录部署名称

3. **获取访问信息**
   - 复制端点URL
   - 复制API密钥
   - 记录API版本

### 网络代理和CORS问题解决方案

#### 内置代理配置
系统已内置代理配置，自动处理CORS问题：

```javascript
// 自动代理配置
const proxyConfig = {
  '/api/openai': 'https://api.openai.com',
  '/api/gemini': 'https://generativelanguage.googleapis.com',
  '/api/azure': 'https://{resource}.openai.azure.com'
}
```

#### 企业网络环境解决方案

1. **配置企业代理**
   ```bash
   # 设置npm代理
   npm config set proxy http://proxy.company.com:8080
   npm config set https-proxy http://proxy.company.com:8080

   # 设置环境变量
   export HTTP_PROXY=http://proxy.company.com:8080
   export HTTPS_PROXY=http://proxy.company.com:8080
   ```

2. **防火墙配置**
   需要开放以下域名的访问权限：
   ```
   - api.openai.com
   - generativelanguage.googleapis.com
   - *.openai.azure.com
   ```

3. **使用代理工具**
   - 推荐使用Clash、V2Ray等代理工具
   - 配置系统代理或应用代理
   - 确保代理稳定性和速度

#### 常见网络问题解决

| 问题 | 症状 | 解决方案 |
|------|------|----------|
| **CORS错误** | 浏览器控制台显示跨域错误 | 使用内置代理，检查端点配置 |
| **连接超时** | 请求长时间无响应 | 检查网络连接，增加超时时间 |
| **403错误** | API访问被拒绝 | 检查API密钥权限和配额 |
| **404错误** | 端点不存在 | 验证端点URL格式是否正确 |
| **429错误** | 请求频率超限 | 等待一段时间后重试，检查配额 |

### 参数配置说明

#### 核心参数
- **Temperature (0-1)**：控制输出随机性
  - 0：最确定性输出，适合专业评估
  - 0.3：推荐值，平衡准确性和多样性
  - 1：最随机输出，创造性更强

- **最大令牌数**：限制响应长度
  - 1000：简短评估
  - 2000：标准评估（推荐）
  - 4000：详细评估

- **超时时间**：API请求超时设置
  - 30秒：标准设置
  - 60秒：复杂评估
  - 120秒：大型病历评估

---

## 📱 界面展示

### 主要功能页面截图说明

#### 🔐 登录页面
- **设计特色**：现代化双栏布局，左侧品牌展示，右侧登录表单
- **功能特点**：
  - 响应式设计，适配移动端
  - 表单验证和错误提示
  - 密码显示/隐藏切换
  - 自动记住登录状态

#### 🏠 主页面（Dashboard）
- **核心功能**：
  - 📊 评估统计数据卡片展示
  - 📈 评分分布图表（柱状图、饼图）
  - 📋 最近评估记录列表
  - ⚡ 快速操作按钮

- **数据展示**：
  - 总评估数量、今日评估、平均评分
  - 评分区间分布统计
  - 评估趋势图表

#### 📝 病历评估页面
- **布局结构**：
  - 左侧：患者信息 + 专家选择 + 病历输入
  - 右侧：实时评估结果展示
  - 固定容器设计，支持滚动查看

- **交互功能**：
  - 专家选择下拉菜单，带预览功能
  - 大文本框支持长病历输入
  - 实时字数统计和格式提示
  - 评估进度指示器

#### 📊 评估结果页面
- **结果展示**：
  - 🎯 总体评分圆形进度图
  - 📈 分项评分详细卡片
  - 📝 AI生成的评估总结
  - 💡 具体改进建议列表
  - 📋 支持性摘录展示

- **交互功能**：
  - 一键复制评估内容
  - 导出评估报告
  - 重新评估功能
  - 详情展开/收起

#### 📚 评估历史页面
- **功能特色**：
  - 📋 表格式历史记录展示
  - 🔍 多维度搜索筛选
  - 📊 统计图表展示
  - 🗂️ 批量操作功能

- **操作功能**：
  - 查看详细评估结果
  - 基于历史重新评估
  - 批量导出和删除
  - 评估趋势分析

#### ⚙️ 系统设置页面
- **配置选项**：
  - 🤖 LLM服务商选择
  - 🔑 API密钥配置
  - 📊 参数调节滑块
  - 🔗 连接测试功能

- **用户体验**：
  - 分步骤配置向导
  - 实时配置验证
  - 配置保存确认
  - 帮助文档链接

#### 👥 用户管理页面（管理员）
- **管理功能**：
  - 👤 用户列表展示
  - ✏️ 用户信息编辑
  - 🔐 权限角色分配
  - 📊 用户活动统计

### 操作流程可视化展示

#### 完整评估流程
```
登录系统 → 选择评估 → 填写患者信息 → 选择专家 → 输入病历 → 开始评估 → 查看结果 → 保存/导出
    ↓         ↓           ↓            ↓         ↓         ↓         ↓         ↓
  身份验证   进入评估页   患者基本信息   评估模式   病历内容   AI分析    详细报告   历史记录
```

#### 系统配置流程
```
进入设置 → 选择服务商 → 输入密钥 → 配置参数 → 测试连接 → 保存配置 → 开始使用
    ↓         ↓          ↓         ↓         ↓         ↓         ↓
  设置页面   API选择    密钥输入   参数调节   连接验证   配置保存   正常使用
```

#### 历史管理流程
```
查看历史 → 搜索筛选 → 选择记录 → 查看详情 → 操作选择 → 执行操作
    ↓         ↓         ↓         ↓         ↓         ↓
  历史页面   条件筛选   记录选择   详情展示   功能选择   完成操作
```

---

## 🤝 技术支持

### 常见问题解答

#### ❓ 评估相关问题

**Q: 评估结果不准确怎么办？**
A:
- 尝试更换不同的专家模板进行评估
- 检查病历内容是否完整和规范
- 使用自定义提示词进行针对性评估
- 确保使用的AI模型版本较新

**Q: 评估速度很慢怎么处理？**
A:
- 检查网络连接稳定性
- 减少病历内容长度
- 降低最大令牌数设置
- 更换网络环境或使用代理

**Q: 如何提高评估质量？**
A:
- 使用更高级的AI模型（如GPT-4）
- 提供完整、规范的病历内容
- 选择合适的评估专家模式
- 适当调整Temperature参数

#### ❓ 配置相关问题

**Q: API调用失败怎么处理？**
A:
- 检查API密钥是否正确和有效
- 验证端点URL格式是否正确
- 确认API配额是否充足
- 检查网络连接和防火墙设置

**Q: 如何获取API密钥？**
A:
- OpenAI：访问platform.openai.com注册获取
- Google Gemini：访问makersuite.google.com获取
- Azure OpenAI：在Azure Portal创建资源获取

**Q: 企业网络环境如何配置？**
A:
- 联系网络管理员开放相关域名访问
- 配置企业代理服务器
- 使用VPN或代理工具
- 考虑本地部署AI模型

#### ❓ 数据相关问题

**Q: 数据存储在哪里？**
A:
- 所有数据存储在浏览器本地存储中
- 不会上传到任何服务器
- 支持数据导出备份
- 清除浏览器数据会丢失所有记录

**Q: 如何备份评估数据？**
A:
- 使用"导出"功能备份单条记录
- 在历史页面批量导出所有记录
- 定期保存重要评估结果
- 建议使用云存储同步备份文件

**Q: 如何迁移数据到新设备？**
A:
- 在原设备导出所有评估历史
- 在新设备导入备份文件
- 重新配置API设置
- 验证数据完整性

### 故障排除指南

#### 🔧 安装和启动问题

| 问题 | 可能原因 | 解决方案 |
|------|----------|----------|
| 依赖安装失败 | Node.js版本过低 | 升级到Node.js 16+ |
| 端口占用 | 3000端口被占用 | 使用其他端口或关闭占用进程 |
| 构建失败 | 内存不足 | 增加Node.js内存限制 |
| 页面空白 | JavaScript错误 | 检查浏览器控制台错误 |

#### 🔧 功能使用问题

| 问题 | 可能原因 | 解决方案 |
|------|----------|----------|
| 登录失败 | 用户名密码错误 | 使用正确的测试账户 |
| 评估无响应 | API配置错误 | 检查API设置和连接 |
| 历史记录丢失 | 浏览器数据清除 | 恢复备份或重新评估 |
| 导出失败 | 浏览器兼容性 | 使用Chrome或Firefox |

#### 🔧 性能优化建议

1. **浏览器优化**
   - 使用最新版本的Chrome或Firefox
   - 启用硬件加速
   - 清理浏览器缓存和Cookie

2. **网络优化**
   - 使用稳定的网络连接
   - 配置合适的代理服务器
   - 避免在网络高峰期使用

3. **系统优化**
   - 关闭不必要的后台程序
   - 确保足够的内存空间
   - 定期清理临时文件

### 联系方式和支持渠道

#### 📧 技术支持
- **邮箱**：<EMAIL>
- **响应时间**：工作日24小时内回复
- **支持语言**：中文、英文

#### 💬 社区支持
- **GitHub Issues**：提交Bug报告和功能请求
- **用户论坛**：与其他用户交流使用经验
- **在线文档**：查看最新的使用指南

#### 🚀 商业支持
- **定制开发**：根据需求定制功能
- **私有部署**：企业级私有化部署服务
- **培训服务**：用户培训和技术指导

---

**📄 版本信息**: v1.0.0
**📅 最后更新**: 2024年1月
**⚖️ 许可证**: MIT License
**🏢 开发团队**: Medical AI Solutions Team

---

<div align="center">

**感谢使用医疗病历评估系统！**

如果这个项目对您有帮助，请给我们一个 ⭐ Star

[🏠 返回顶部](#医疗病历评估系统-) · [📖 使用教程](#功能使用教程) · [🔧 配置说明](#api配置说明) · [🤝 技术支持](#技术支持)

</div>


