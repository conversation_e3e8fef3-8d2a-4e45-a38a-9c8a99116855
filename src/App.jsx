import React from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { AuthProvider } from './contexts/AuthContext'
import ProtectedRoute from './components/ProtectedRoute'
import Layout from './components/Layout'
import Login from './pages/Login'
import Dashboard from './pages/Dashboard'
import Evaluator from './pages/Evaluator'
import History from './pages/History'
import HistoryDemo from './pages/HistoryDemo'
import ExpertSelectDemo from './pages/ExpertSelectDemo'
import AuthDemo from './pages/AuthDemo'
import UserManagement from './pages/UserManagement'
import Settings from './pages/Settings'

function App() {
  return (
    <AuthProvider>
      <Routes>
        {/* 登录页面 */}
        <Route path="/login" element={<Login />} />

        {/* 受保护的路由 */}
        <Route path="/*" element={
          <ProtectedRoute>
            <Layout>
              <Routes>
                <Route path="/" element={<Dashboard />} />
                <Route path="/evaluator" element={<Evaluator />} />
                <Route path="/history" element={<History />} />
                <Route path="/history-demo" element={<HistoryDemo />} />
                <Route path="/expert-demo" element={<ExpertSelectDemo />} />
                <Route path="/auth-demo" element={<AuthDemo />} />
                <Route path="/users" element={
                  <ProtectedRoute requiredPermission="manage_users">
                    <UserManagement />
                  </ProtectedRoute>
                } />
                <Route path="/settings" element={
                  <ProtectedRoute requiredPermission="manage_settings">
                    <Settings />
                  </ProtectedRoute>
                } />
                {/* 默认重定向到首页 */}
                <Route path="*" element={<Navigate to="/" replace />} />
              </Routes>
            </Layout>
          </ProtectedRoute>
        } />
      </Routes>
    </AuthProvider>
  )
}

export default App
