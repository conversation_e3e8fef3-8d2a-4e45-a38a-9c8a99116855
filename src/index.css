@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: 'Inter', system-ui, sans-serif;
  font-feature-settings: "rlig" 1, "calt" 1;
}

/* Button styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s;
  outline: none;
  border: none;
  cursor: pointer;
}

.btn:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.btn:disabled {
  opacity: 0.5;
  pointer-events: none;
}

.btn-primary {
  background-color: #2563eb;
  color: white;
  height: 2.5rem;
  padding: 0.5rem 1rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-primary:hover {
  background-color: #1d4ed8;
}

.btn-primary svg {
  flex-shrink: 0;
}

.btn-secondary {
  background-color: #f3f4f6;
  color: #111827;
  height: 2.5rem;
  padding: 0.5rem 1rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-secondary:hover {
  background-color: #e5e7eb;
}

.btn-secondary svg {
  flex-shrink: 0;
}

.btn-outline {
  border: 1px solid #d1d5db;
  background-color: white;
  color: #374151;
  height: 2.5rem;
  padding: 0.5rem 1rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-outline:hover {
  background-color: #f9fafb;
  border-color: #9ca3af;
}

.btn-outline svg {
  flex-shrink: 0;
}

.btn-danger {
  background-color: #dc2626;
  color: white;
  height: 2.5rem;
  padding: 0.5rem 1rem;
}

.btn-danger:hover {
  background-color: #b91c1c;
}

/* Card styles */
.card {
  background-color: white;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

/* Input styles */
.input {
  display: flex;
  height: 2.5rem;
  width: 100%;
  border-radius: 0.375rem;
  border: 1px solid #d1d5db;
  background-color: white;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
}

.input::placeholder {
  color: #6b7280;
}

.input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.input:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

/* Select styles */
.select {
  display: flex;
  height: 2.5rem;
  width: 100%;
  border-radius: 0.375rem;
  border: 1px solid #d1d5db;
  background-color: white;
  padding: 0.5rem 2.5rem 0.5rem 0.75rem;
  font-size: 0.875rem;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

.select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.select:hover {
  border-color: #9ca3af;
}

.select:disabled {
  cursor: not-allowed;
  opacity: 0.5;
  background-color: #f9fafb;
}

/* Custom Select Component */
.custom-select {
  position: relative;
  display: inline-block;
  width: 100%;
}

.custom-select-trigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 2.5rem;
  padding: 0.5rem 0.75rem;
  background-color: white;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

.custom-select-trigger:hover {
  border-color: #9ca3af;
}

.custom-select-trigger:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.custom-select-trigger.open {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.custom-select-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 50;
  margin-top: 0.25rem;
  background-color: white;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  max-height: 16rem;
  overflow-y: auto;
}

.custom-select-option {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.15s ease-in-out;
  border-bottom: 1px solid #f3f4f6;
}

.custom-select-option:last-child {
  border-bottom: none;
}

.custom-select-option:hover {
  background-color: #f9fafb;
}

.custom-select-option.selected {
  background-color: #eff6ff;
  color: #1d4ed8;
}

.custom-select-option.focused {
  background-color: #f3f4f6;
}

.custom-select-search {
  padding: 0.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.custom-select-search input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.25rem;
  font-size: 0.875rem;
}

.custom-select-search input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.1);
}

.custom-select-empty {
  padding: 1rem;
  text-align: center;
  color: #6b7280;
  font-size: 0.875rem;
}

/* Textarea styles */
.textarea {
  display: flex;
  min-height: 5rem;
  width: 100%;
  border-radius: 0.375rem;
  border: 1px solid #d1d5db;
  background-color: white;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  resize: vertical;
}

.textarea::placeholder {
  color: #6b7280;
}

.textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.textarea:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}
/* Gradient styles */
.medical-gradient {
  background: linear-gradient(to right, #0ea5e9, #3b82f6);
}

/* Badge styles */
.status-badge {
  display: inline-flex;
  align-items: center;
  border-radius: 9999px;
  padding: 0.125rem 0.625rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-success {
  background-color: #dcfce7;
  color: #166534;
}

.status-warning {
  background-color: #fef3c7;
  color: #92400e;
}

.status-danger {
  background-color: #fee2e2;
  color: #991b1b;
}

.status-info {
  background-color: #e0f2fe;
  color: #075985;
}

/* Custom scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Loading animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Fade in animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* Login Page Specific Animations */
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-slide-in-left {
  animation: slideInLeft 0.8s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.8s ease-out;
}

/* Login Form Enhancements */
.login-form-card {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
}

.social-login-btn {
  transition: all 0.2s ease-in-out;
}

.social-login-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.user-card {
  transition: all 0.3s ease-in-out;
}

.user-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Evaluation Result Container Styles */
.evaluation-result-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 80vh;
  min-height: 600px;
}

.evaluation-result-header {
  flex-shrink: 0;
  position: sticky;
  top: 0;
  z-index: 10;
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 0;
}

.evaluation-result-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem 0;
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

/* Custom Scrollbar for Webkit browsers */
.evaluation-result-content::-webkit-scrollbar {
  width: 8px;
}

.evaluation-result-content::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.evaluation-result-content::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.evaluation-result-content::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Smooth scrolling behavior */
.evaluation-result-content {
  scroll-behavior: smooth;
}

/* Enhanced card spacing within scrollable area */
.evaluation-result-content .card {
  margin-bottom: 1.5rem;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  background: white;
}

.evaluation-result-content .card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

/* Fade effect for scrollable content */
.evaluation-result-content::before {
  content: '';
  position: sticky;
  top: 0;
  height: 20px;
  background: linear-gradient(to bottom, white, transparent);
  z-index: 5;
  display: block;
  margin-bottom: -20px;
}

.evaluation-result-content::after {
  content: '';
  position: sticky;
  bottom: 0;
  height: 20px;
  background: linear-gradient(to top, white, transparent);
  z-index: 5;
  display: block;
  margin-top: -20px;
}

/* Improved text readability */
.evaluation-result-content .card h4 {
  color: #1f2937;
  font-weight: 600;
}

.evaluation-result-content .card p {
  line-height: 1.6;
}

/* Enhanced button styles within evaluation results */
.evaluation-result-content .btn-outline {
  border: 1px solid #d1d5db;
  background: white;
  transition: all 0.2s ease;
}

.evaluation-result-content .btn-outline:hover {
  background: #f9fafb;
  border-color: #9ca3af;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .evaluation-result-container {
    max-height: 70vh;
    min-height: 500px;
  }

  .evaluation-result-content {
    padding: 0.75rem 0;
  }
}

@media (max-width: 640px) {
  .evaluation-result-container {
    max-height: 65vh;
    min-height: 400px;
  }
}

/* Results Section Container */
.results-section-container {
  height: 80vh;
  min-height: 600px;
  display: flex;
  flex-direction: column;
}

.results-section-container .card {
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Responsive adjustments for results section */
@media (max-width: 768px) {
  .results-section-container {
    height: 70vh;
    min-height: 500px;
  }
}

@media (max-width: 640px) {
  .results-section-container {
    height: 65vh;
    min-height: 400px;
  }

  /* Mobile optimizations for evaluation results */
  .evaluation-result-content .card {
    margin-bottom: 1rem;
    padding: 1rem !important;
  }

  .evaluation-result-content .card h4 {
    font-size: 1rem;
  }

  .evaluation-result-content .card p {
    font-size: 0.875rem;
  }

  /* Adjust button sizes on mobile */
  .evaluation-result-content .btn-outline {
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
  }

  /* Optimize spacing for mobile */
  .evaluation-result-content .space-y-3 > * + * {
    margin-top: 0.75rem;
  }

  .evaluation-result-content .space-y-4 > * + * {
    margin-top: 1rem;
  }
}

/* Animation keyframes for evaluation results */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Animation classes */
.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.5s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.7s ease-out;
}

/* Staggered animation for cards */
.evaluation-result-content .card:nth-child(1) {
  animation: slideUp 0.6s ease-out 0.1s both;
}

.evaluation-result-content .card:nth-child(2) {
  animation: slideUp 0.6s ease-out 0.2s both;
}

.evaluation-result-content .card:nth-child(3) {
  animation: slideUp 0.6s ease-out 0.3s both;
}

.evaluation-result-content .card:nth-child(4) {
  animation: slideUp 0.6s ease-out 0.4s both;
}

/* Medical Record Content Styles */
.medical-record-content {
  font-family: 'JetBrains Mono', monospace;
  line-height: 1.6;
  white-space: pre-wrap;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.medical-record-expanded {
  max-height: 24rem; /* 96 * 0.25rem */
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.medical-record-expanded::-webkit-scrollbar {
  width: 6px;
}

.medical-record-expanded::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.medical-record-expanded::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.medical-record-expanded::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.medical-record-collapsed {
  max-height: 8rem; /* 32 * 0.25rem */
  overflow: hidden;
  position: relative;
}

.medical-record-fade-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2rem;
  background: linear-gradient(to bottom, transparent, white);
  pointer-events: none;
}

/* Search Highlight Styles */
mark {
  background-color: #fef3c7;
  color: #92400e;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-weight: 500;
}

/* Responsive Text Sizes */
@media (max-width: 640px) {
  .medical-record-content {
    font-size: 0.75rem;
  }

  .medical-record-expanded {
    max-height: 16rem;
  }

  .medical-record-collapsed {
    max-height: 6rem;
  }
}

/* Enhanced Animation for Medical Record Expansion */
@keyframes expand {
  from {
    max-height: 0;
    opacity: 0;
  }
  to {
    max-height: 24rem;
    opacity: 1;
  }
}

.animate-expand {
  animation: expand 0.3s ease-out;
}

/* Prompt Editor Styles */
.prompt-editor {
  font-family: 'JetBrains Mono', 'Consolas', 'Monaco', 'Courier New', monospace;
}

.prompt-editor-toolbar {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 1px solid #e2e8f0;
}

.prompt-editor-textarea {
  font-family: inherit;
  line-height: 1.6;
  tab-size: 2;
  -moz-tab-size: 2;
  resize: none;
  outline: none;
  border: none;
  background: transparent;
  color: #1f2937;
}

.prompt-editor-textarea:focus {
  outline: none;
  box-shadow: none;
}

.prompt-editor-textarea::placeholder {
  color: #9ca3af;
  font-style: italic;
}

/* 编辑器滚动条样式 */
.prompt-editor-textarea::-webkit-scrollbar {
  width: 8px;
}

.prompt-editor-textarea::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.prompt-editor-textarea::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.prompt-editor-textarea::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 拖拽调整手柄 */
.prompt-editor-resize-handle {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  transition: all 0.2s ease;
  cursor: ns-resize;
}

.prompt-editor-resize-handle:hover {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
}

.prompt-editor-resize-handle::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 32px;
  height: 4px;
  background: #94a3b8;
  border-radius: 2px;
  opacity: 0.6;
}

.prompt-editor-resize-handle:hover::before {
  opacity: 1;
}

/* 工具栏按钮样式 */
.prompt-editor-btn {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
  color: #6b7280;
  background: transparent;
  border: none;
  cursor: pointer;
}

.prompt-editor-btn:hover {
  color: #374151;
  background: #f3f4f6;
}

.prompt-editor-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.prompt-editor-btn.active {
  color: #2563eb;
  background: #dbeafe;
}

/* 状态指示器 */
.prompt-editor-status {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.prompt-editor-status.dirty {
  color: #d97706;
  background: #fef3c7;
}

.prompt-editor-status.saved {
  color: #059669;
  background: #d1fae5;
}

/* 统计信息栏 */
.prompt-editor-stats {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-top: 1px solid #e2e8f0;
  font-family: 'Inter', sans-serif;
}

/* 预览区域 */
.prompt-editor-preview {
  background: #f8fafc;
  border-left: 1px solid #e2e8f0;
}

.prompt-editor-preview-content {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  font-family: 'JetBrains Mono', monospace;
  line-height: 1.6;
  color: #374151;
}

/* 全屏模式样式 */
.prompt-editor-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: white;
}

.prompt-editor-fullscreen .prompt-editor-toolbar {
  border-radius: 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .prompt-editor-toolbar {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .prompt-editor-btn {
    padding: 0.375rem;
  }

  .prompt-editor-stats {
    font-size: 0.75rem;
  }

  .prompt-editor-preview {
    display: none;
  }
}

/* 动画效果 */
@keyframes prompt-editor-fade-in {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.prompt-editor-fade-in {
  animation: prompt-editor-fade-in 0.2s ease-out;
}

/* Role Selection Styles */
.role-option {
  transition: all 0.2s ease-in-out;
}

.role-option:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.role-option.selected {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Role-specific color enhancements */
.role-admin {
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  border-color: #fca5a5;
}

.role-admin:hover {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  border-color: #f87171;
}

.role-doctor {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border-color: #93c5fd;
}

.role-doctor:hover {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border-color: #60a5fa;
}

.role-nurse {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  border-color: #86efac;
}

.role-nurse:hover {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
  border-color: #4ade80;
}

.role-all {
  background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
  border-color: #d1d5db;
}

.role-all:hover {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  border-color: #9ca3af;
}

/* Role badge animations */
.role-badge {
  animation: fadeInScale 0.3s ease-out;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Enhanced custom select for role selection */
.custom-select .role-option {
  border-radius: 0.5rem;
  margin: 0.25rem;
}

.custom-select-option:has(.role-option) {
  padding: 0.5rem;
  border-radius: 0.5rem;
}

.custom-select-option:has(.role-option):hover {
  background-color: transparent;
}

/* Role icon animations */
.role-icon {
  transition: all 0.2s ease-in-out;
}

.role-option:hover .role-icon {
  transform: scale(1.1);
}

/* Responsive adjustments for role selection */
@media (max-width: 640px) {
  .role-option {
    padding: 0.75rem;
  }

  .role-option .role-badge {
    display: none;
  }
}
