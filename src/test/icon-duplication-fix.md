# 图标重复问题修复

## 问题描述
在专家选择下拉组件中，每个选项都显示了两个相同的图标，造成视觉重复。

## 问题原因
CustomSelect 组件在渲染选项时存在双重图标渲染：

1. **第一次渲染**：在 `CustomSelect.jsx` 第184行
   ```jsx
   {option.icon && <option.icon className="h-4 w-4 flex-shrink-0" />}
   ```

2. **第二次渲染**：在自定义的 `optionRender` 函数中
   ```jsx
   <option.icon className="h-4 w-4 text-blue-600" />
   ```

## 修复方案
修改 `CustomSelect.jsx` 组件逻辑：
- 当提供了 `optionRender` 函数时，不自动渲染图标
- 只在默认渲染模式下才显示图标

## 修复前的代码结构
```jsx
<div className="flex items-center space-x-2 flex-1">
  {option.icon && <option.icon />}  // 自动渲染图标
  <div>
    {optionRender ? optionRender(option) : defaultRender}  // 可能再次渲染图标
  </div>
</div>
```

## 修复后的代码结构
```jsx
<div className="flex-1">
  {optionRender ? optionRender(option) : (
    <div className="flex items-center space-x-2">
      {option.icon && <option.icon />}  // 只在默认模式下渲染图标
      <div>defaultRender</div>
    </div>
  )}
</div>
```

## 修复效果
- ✅ 每个选项只显示一个图标
- ✅ 保持了自定义渲染的灵活性
- ✅ 不影响默认渲染模式
- ✅ 视觉效果更加清晰

## 测试页面
- 主要页面：`/evaluator`
- 演示页面：`/expert-demo`

## 相关文件
- `src/components/CustomSelect.jsx` - 核心修复
- `src/pages/Evaluator.jsx` - 使用自定义渲染
- `src/pages/ExpertSelectDemo.jsx` - 演示页面
