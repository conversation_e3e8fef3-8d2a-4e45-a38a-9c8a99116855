import React, { useState, useEffect } from 'react'
import { <PERSON> } from 'react-router-dom'
import {
  <PERSON>Text,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
  BarChart3,
  Activity,
  Shield
} from 'lucide-react'
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts'

const Dashboard = () => {
  const [stats, setStats] = useState({
    totalEvaluations: 0,
    todayEvaluations: 0,
    averageScore: 0,
    pendingReviews: 0
  })

  const [recentEvaluations, setRecentEvaluations] = useState([])
  const [scoreDistribution, setScoreDistribution] = useState([])

  useEffect(() => {
    // Load dashboard data from localStorage or API
    loadDashboardData()
  }, [])

  const loadDashboardData = () => {
    // Get evaluation history from localStorage
    const history = JSON.parse(localStorage.getItem('evaluationHistory') || '[]')
    
    const today = new Date().toDateString()
    const todayEvals = history.filter(evaluation => new Date(evaluation.timestamp).toDateString() === today)

    const totalScore = history.reduce((sum, evaluation) => sum + evaluation.overallScore, 0)
    const avgScore = history.length > 0 ? (totalScore / history.length).toFixed(1) : 0

    setStats({
      totalEvaluations: history.length,
      todayEvaluations: todayEvals.length,
      averageScore: avgScore,
      pendingReviews: Math.floor(Math.random() * 5) // Mock data
    })

    // Get recent evaluations (last 5)
    setRecentEvaluations(history.slice(-5).reverse())

    // Calculate score distribution
    const distribution = [
      { name: '优秀 (90-100)', value: 0, color: '#22c55e' },
      { name: '良好 (80-89)', value: 0, color: '#3b82f6' },
      { name: '一般 (70-79)', value: 0, color: '#f59e0b' },
      { name: '需改进 (<70)', value: 0, color: '#ef4444' }
    ]

    history.forEach(evaluation => {
      if (evaluation.overallScore >= 90) distribution[0].value++
      else if (evaluation.overallScore >= 80) distribution[1].value++
      else if (evaluation.overallScore >= 70) distribution[2].value++
      else distribution[3].value++
    })

    setScoreDistribution(distribution.filter(item => item.value > 0))
  }

  const StatCard = ({ title, value, icon: Icon, color, trend }) => (
    <div className="card p-6">
      <div className="flex items-center">
        <div className={`flex-shrink-0 p-3 rounded-lg ${color}`}>
          <Icon className="h-6 w-6 text-white" />
        </div>
        <div className="ml-4 flex-1">
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <div className="flex items-baseline">
            <p className="text-2xl font-semibold text-gray-900">{value}</p>
            {trend && (
              <p className={`ml-2 text-sm ${trend > 0 ? 'text-success-600' : 'text-danger-600'}`}>
                {trend > 0 ? '+' : ''}{trend}%
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  )

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="medical-gradient rounded-lg p-6 text-white">
        <h1 className="text-2xl font-bold">欢迎使用病历评估助手</h1>
        <p className="mt-2 text-medical-100">
          智能化病历评估系统，帮助医疗专业人员提高病历质量和诊疗效率
        </p>
        <div className="mt-4">
          <Link
            to="/evaluator"
            className="inline-flex items-center px-4 py-2 bg-white text-medical-600 rounded-md hover:bg-gray-100 transition-colors"
          >
            <FileText className="h-4 w-4 mr-2" />
            开始评估病历
          </Link>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="总评估数"
          value={stats.totalEvaluations}
          icon={FileText}
          color="bg-medical-500"
        />
        <StatCard
          title="今日评估"
          value={stats.todayEvaluations}
          icon={TrendingUp}
          color="bg-success-500"
        />
        <StatCard
          title="平均评分"
          value={`${stats.averageScore}分`}
          icon={BarChart3}
          color="bg-primary-500"
        />
        <StatCard
          title="待审核"
          value={stats.pendingReviews}
          icon={Clock}
          color="bg-warning-500"
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Score Distribution Chart */}
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">评分分布</h3>
          {scoreDistribution.length > 0 ? (
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={scoreDistribution}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {scoreDistribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          ) : (
            <div className="flex items-center justify-center h-64 text-gray-500">
              <div className="text-center">
                <Activity className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>暂无评估数据</p>
              </div>
            </div>
          )}
        </div>

        {/* Recent Evaluations */}
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">最近评估</h3>
          {recentEvaluations.length > 0 ? (
            <div className="space-y-4">
              {recentEvaluations.map((evaluation, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">
                      病历评估 #{evaluation.id?.slice(-6) || index + 1}
                    </p>
                    <p className="text-xs text-gray-500">
                      {new Date(evaluation.timestamp).toLocaleString('zh-CN')}
                    </p>
                  </div>
                  <div className="flex items-center">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      evaluation.overallScore >= 90 ? 'bg-success-100 text-success-800' :
                      evaluation.overallScore >= 80 ? 'bg-primary-100 text-primary-800' :
                      evaluation.overallScore >= 70 ? 'bg-warning-100 text-warning-800' :
                      'bg-danger-100 text-danger-800'
                    }`}>
                      {evaluation.overallScore}分
                    </span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex items-center justify-center h-64 text-gray-500">
              <div className="text-center">
                <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>暂无评估记录</p>
                <Link
                  to="/evaluator"
                  className="mt-2 text-medical-600 hover:text-medical-700 text-sm"
                >
                  开始第一次评估
                </Link>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">快速操作</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <Link
            to="/evaluator"
            className="flex items-center p-4 bg-medical-50 rounded-lg hover:bg-medical-100 transition-colors"
          >
            <FileText className="h-8 w-8 text-medical-600 mr-3" />
            <div>
              <p className="font-medium text-gray-900">新建评估</p>
              <p className="text-sm text-gray-600">开始评估新的病历</p>
            </div>
          </Link>
          <Link
            to="/history"
            className="flex items-center p-4 bg-primary-50 rounded-lg hover:bg-primary-100 transition-colors"
          >
            <Clock className="h-8 w-8 text-primary-600 mr-3" />
            <div>
              <p className="font-medium text-gray-900">查看历史</p>
              <p className="text-sm text-gray-600">浏览历史评估记录</p>
            </div>
          </Link>
          <Link
            to="/settings"
            className="flex items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <AlertCircle className="h-8 w-8 text-gray-600 mr-3" />
            <div>
              <p className="font-medium text-gray-900">系统设置</p>
              <p className="text-sm text-gray-600">配置模型和参数</p>
            </div>
          </Link>
          <Link
            to="/auth-demo"
            className="flex items-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors"
          >
            <Shield className="h-8 w-8 text-purple-600 mr-3" />
            <div>
              <p className="font-medium text-gray-900">认证演示</p>
              <p className="text-sm text-gray-600">查看用户权限信息</p>
            </div>
          </Link>
        </div>
      </div>
    </div>
  )
}

export default Dashboard
