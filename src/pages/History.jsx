import React, { useState, useEffect } from 'react'
import {
  History as HistoryIcon,
  Search,
  Filter,
  Download,
  Trash2,
  Eye,
  Calendar,
  FileText,
  TrendingUp,
  MoreVertical,
  ChevronDown,
  ChevronUp,
  Star,
  Award,
  AlertTriangle,
  Target,
  Stethoscope
} from 'lucide-react'
import toast from 'react-hot-toast'
import { format } from 'date-fns'
import CustomSelect from '../components/CustomSelect'

const History = () => {
  const [evaluations, setEvaluations] = useState([])
  const [filteredEvaluations, setFilteredEvaluations] = useState([])
  const [searchTerm, setSearchTerm] = useState('')
  const [sortBy, setSortBy] = useState('timestamp')
  const [sortOrder, setSortOrder] = useState('desc')
  const [filterScore, setFilterScore] = useState('all')
  const [filterType, setFilterType] = useState('all')
  const [selectedEvaluations, setSelectedEvaluations] = useState([])
  const [expandedEvaluation, setExpandedEvaluation] = useState(null)

  useEffect(() => {
    loadEvaluations()
  }, [])

  useEffect(() => {
    filterAndSortEvaluations()
  }, [evaluations, searchTerm, sortBy, sortOrder, filterScore, filterType])

  const loadEvaluations = () => {
    const history = JSON.parse(localStorage.getItem('evaluationHistory') || '[]')
    setEvaluations(history)
  }

  const filterAndSortEvaluations = () => {
    let filtered = [...evaluations]

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(evaluation =>
        evaluation.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
        evaluation.patientInfo?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        evaluation.id.includes(searchTerm)
      )
    }

    // Score filter
    if (filterScore !== 'all') {
      const [min, max] = filterScore.split('-').map(Number)
      filtered = filtered.filter(evaluation => {
        const score = evaluation.result?.overallScore || evaluation.overallScore || 0
        return max ? (score >= min && score <= max) : score >= min
      })
    }

    // Type filter
    if (filterType !== 'all') {
      filtered = filtered.filter(evaluation => evaluation.evaluationType === filterType)
    }

    // Sort
    filtered.sort((a, b) => {
      let aValue, bValue
      
      switch (sortBy) {
        case 'timestamp':
          aValue = new Date(a.timestamp)
          bValue = new Date(b.timestamp)
          break
        case 'score':
          aValue = a.result?.overallScore || a.overallScore || 0
          bValue = b.result?.overallScore || b.overallScore || 0
          break
        case 'type':
          aValue = a.evaluationType || ''
          bValue = b.evaluationType || ''
          break
        default:
          aValue = a[sortBy] || ''
          bValue = b[sortBy] || ''
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })

    setFilteredEvaluations(filtered)
  }

  const handleDeleteEvaluation = (id) => {
    if (window.confirm('确定要删除这条评估记录吗？')) {
      const updatedEvaluations = evaluations.filter(evaluation => evaluation.id !== id)
      setEvaluations(updatedEvaluations)
      localStorage.setItem('evaluationHistory', JSON.stringify(updatedEvaluations))
      toast.success('评估记录已删除')
    }
  }

  const handleBatchDelete = () => {
    if (selectedEvaluations.length === 0) {
      toast.error('请先选择要删除的记录')
      return
    }

    if (window.confirm(`确定要删除选中的 ${selectedEvaluations.length} 条记录吗？`)) {
      const updatedEvaluations = evaluations.filter(evaluation => !selectedEvaluations.includes(evaluation.id))
      setEvaluations(updatedEvaluations)
      localStorage.setItem('evaluationHistory', JSON.stringify(updatedEvaluations))
      setSelectedEvaluations([])
      toast.success(`已删除 ${selectedEvaluations.length} 条记录`)
    }
  }

  const handleExportHistory = () => {
    const dataToExport = selectedEvaluations.length > 0
      ? evaluations.filter(evaluation => selectedEvaluations.includes(evaluation.id))
      : evaluations

    const blob = new Blob([JSON.stringify(dataToExport, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `evaluation-history-${format(new Date(), 'yyyy-MM-dd')}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    toast.success('历史记录已导出')
  }

  const getScoreColor = (score) => {
    if (score >= 90) return 'text-success-600 bg-success-100'
    if (score >= 80) return 'text-primary-600 bg-primary-100'
    if (score >= 70) return 'text-warning-600 bg-warning-100'
    return 'text-danger-600 bg-danger-100'
  }

  const getTypeLabel = (type) => {
    const types = {
      comprehensive: '综合评估',
      clinical: '临床质量评估',
      documentation: '文档规范评估'
    }
    return types[type] || type
  }

  const toggleEvaluationSelection = (id) => {
    setSelectedEvaluations(prev => 
      prev.includes(id) 
        ? prev.filter(evalId => evalId !== id)
        : [...prev, id]
    )
  }

  const toggleSelectAll = () => {
    if (selectedEvaluations.length === filteredEvaluations.length) {
      setSelectedEvaluations([])
    } else {
      setSelectedEvaluations(filteredEvaluations.map(evaluation => evaluation.id))
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="card p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">评估历史</h1>
            <p className="mt-1 text-gray-600">
              查看和管理所有病历评估记录，共 {evaluations.length} 条记录
            </p>
          </div>
          <HistoryIcon className="h-8 w-8 text-gray-400" />
        </div>
      </div>

      {/* Filters and Search */}
      <div className="card p-6">
        <div className="space-y-4">
          {/* Filter Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Filter className="h-5 w-5 text-gray-500" />
              <h3 className="text-lg font-medium text-gray-900">筛选和搜索</h3>
            </div>
            <div className="text-sm text-gray-500">
              显示 {filteredEvaluations.length} / {evaluations.length} 条记录
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="搜索病历内容或ID..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="input pl-10"
              />
            </div>

            {/* Score Filter */}
            <CustomSelect
              options={[
                {
                  value: 'all',
                  label: '所有评分',
                  description: '显示所有评分范围',
                  icon: Target
                },
                {
                  value: '90-100',
                  label: '优秀 (90-100分)',
                  description: '高质量病历记录',
                  icon: Award
                },
                {
                  value: '80-89',
                  label: '良好 (80-89分)',
                  description: '质量较好的病历记录',
                  icon: Star
                },
                {
                  value: '70-79',
                  label: '一般 (70-79分)',
                  description: '基本合格的病历记录',
                  icon: TrendingUp
                },
                {
                  value: '0-69',
                  label: '需改进 (<70分)',
                  description: '需要改进的病历记录',
                  icon: AlertTriangle
                }
              ]}
              value={filterScore}
              onChange={setFilterScore}
              placeholder="选择评分范围"
              optionRender={(option) => (
                <div className="flex items-center space-x-3">
                  <option.icon className={`h-4 w-4 ${
                    option.value === '90-100' ? 'text-green-600' :
                    option.value === '80-89' ? 'text-blue-600' :
                    option.value === '70-79' ? 'text-yellow-600' :
                    option.value === '0-69' ? 'text-red-600' :
                    'text-gray-500'
                  }`} />
                  <div>
                    <div className="font-medium text-gray-900">{option.label}</div>
                    <div className="text-sm text-gray-500">{option.description}</div>
                  </div>
                </div>
              )}
            />

            {/* Type Filter */}
            <CustomSelect
              options={[
                {
                  value: 'all',
                  label: '所有类型',
                  description: '显示所有评估类型',
                  icon: FileText
                },
                {
                  value: 'comprehensive',
                  label: '综合评估',
                  description: '全面的病历质量评估',
                  icon: Target
                },
                {
                  value: 'clinical',
                  label: '临床质量评估',
                  description: '专注临床诊疗质量',
                  icon: Stethoscope
                },
                {
                  value: 'documentation',
                  label: '文档规范评估',
                  description: '文档书写规范性评估',
                  icon: FileText
                }
              ]}
              value={filterType}
              onChange={setFilterType}
              placeholder="选择评估类型"
              optionRender={(option) => (
                <div className="flex items-center space-x-3">
                  <option.icon className={`h-4 w-4 ${
                    option.value === 'comprehensive' ? 'text-blue-600' :
                    option.value === 'clinical' ? 'text-green-600' :
                    option.value === 'documentation' ? 'text-purple-600' :
                    'text-gray-500'
                  }`} />
                  <div>
                    <div className="font-medium text-gray-900">{option.label}</div>
                    <div className="text-sm text-gray-500">{option.description}</div>
                  </div>
                </div>
              )}
            />

            {/* Sort */}
            <div className="flex space-x-2">
              <CustomSelect
                options={[
                  {
                    value: 'timestamp',
                    label: '按时间',
                    description: '按评估时间排序',
                    icon: Calendar
                  },
                  {
                    value: 'score',
                    label: '按评分',
                    description: '按评估分数排序',
                    icon: Star
                  },
                  {
                    value: 'type',
                    label: '按类型',
                    description: '按评估类型排序',
                    icon: Filter
                  }
                ]}
                value={sortBy}
                onChange={setSortBy}
                placeholder="排序方式"
                className="flex-1"
                optionRender={(option) => (
                  <div className="flex items-center space-x-3">
                    <option.icon className="h-4 w-4 text-gray-500" />
                    <div>
                      <div className="font-medium text-gray-900">{option.label}</div>
                      <div className="text-sm text-gray-500">{option.description}</div>
                    </div>
                  </div>
                )}
              />
              <button
                onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                className="btn-outline px-3 flex items-center justify-center"
                title={sortOrder === 'asc' ? '升序排列' : '降序排列'}
              >
                {sortOrder === 'asc' ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
              </button>
            </div>
          </div>

          {/* Active Filters Display */}
          {(searchTerm || filterScore !== 'all' || filterType !== 'all') && (
            <div className="flex items-center space-x-2 pt-2 border-t border-gray-200">
              <span className="text-sm text-gray-500">当前筛选:</span>
              {searchTerm && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  搜索: {searchTerm}
                  <button
                    onClick={() => setSearchTerm('')}
                    className="ml-1 text-blue-600 hover:text-blue-800"
                  >
                    ×
                  </button>
                </span>
              )}
              {filterScore !== 'all' && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  评分: {filterScore === '90-100' ? '优秀' : filterScore === '80-89' ? '良好' : filterScore === '70-79' ? '一般' : '需改进'}
                  <button
                    onClick={() => setFilterScore('all')}
                    className="ml-1 text-green-600 hover:text-green-800"
                  >
                    ×
                  </button>
                </span>
              )}
              {filterType !== 'all' && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                  类型: {getTypeLabel(filterType)}
                  <button
                    onClick={() => setFilterType('all')}
                    className="ml-1 text-purple-600 hover:text-purple-800"
                  >
                    ×
                  </button>
                </span>
              )}
              <button
                onClick={() => {
                  setSearchTerm('')
                  setFilterScore('all')
                  setFilterType('all')
                }}
                className="text-xs text-gray-500 hover:text-gray-700 underline"
              >
                清除所有筛选
              </button>
            </div>
          )}
        </div>

        {/* Batch Actions */}
        {selectedEvaluations.length > 0 && (
          <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full">
                  <span className="text-sm font-semibold text-blue-600">{selectedEvaluations.length}</span>
                </div>
                <div>
                  <p className="text-sm font-medium text-blue-900">
                    已选择 {selectedEvaluations.length} 条记录
                  </p>
                  <p className="text-xs text-blue-600">
                    可以批量导出或删除选中的评估记录
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <button
                  onClick={handleExportHistory}
                  className="inline-flex items-center px-3 py-2 text-sm font-medium text-blue-700 bg-white border border-blue-300 rounded-lg hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
                >
                  <Download className="h-4 w-4 mr-2" />
                  导出选中
                </button>
                <button
                  onClick={handleBatchDelete}
                  className="inline-flex items-center px-3 py-2 text-sm font-medium text-red-700 bg-white border border-red-300 rounded-lg hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  删除选中
                </button>
                <button
                  onClick={() => setSelectedEvaluations([])}
                  className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                >
                  取消选择
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Evaluation List */}
      <div className="card overflow-hidden">
        {filteredEvaluations.length > 0 ? (
          <div className="divide-y divide-gray-200">
            {/* Header */}
            <div className="px-6 py-4 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={selectedEvaluations.length === filteredEvaluations.length && filteredEvaluations.length > 0}
                    onChange={toggleSelectAll}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded transition-colors"
                  />
                  <span className="text-sm font-medium text-gray-700">
                    {selectedEvaluations.length === filteredEvaluations.length && filteredEvaluations.length > 0
                      ? '取消全选'
                      : '全选'
                    }
                  </span>
                  {selectedEvaluations.length > 0 && (
                    <span className="text-xs text-gray-500">
                      ({selectedEvaluations.length}/{filteredEvaluations.length})
                    </span>
                  )}
                </div>
                <div className="flex items-center space-x-4 text-xs text-gray-500">
                  <span>评估时间</span>
                  <span>类型</span>
                  <span>评分</span>
                  <span>操作</span>
                </div>
              </div>
            </div>

            {/* Evaluation Items */}
            {filteredEvaluations.map((evaluation) => {
              const score = evaluation.result?.overallScore || evaluation.overallScore || 0
              const isExpanded = expandedEvaluation === evaluation.id
              const isSelected = selectedEvaluations.includes(evaluation.id)

              return (
                <div
                  key={evaluation.id}
                  className={`transition-all duration-200 ${
                    isSelected ? 'bg-blue-50 border-l-4 border-l-blue-500' : 'hover:bg-gray-50'
                  }`}
                >
                  <div className="px-6 py-4">
                    <div className="flex items-start space-x-4">
                      <div className="flex items-center pt-1">
                        <input
                          type="checkbox"
                          checked={isSelected}
                          onChange={() => toggleEvaluationSelection(evaluation.id)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded transition-colors"
                        />
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center space-x-3 mb-2">
                              <h4 className="text-sm font-semibold text-gray-900">
                                评估 #{evaluation.id.slice(-6)}
                              </h4>
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                evaluation.evaluationType === 'comprehensive' ? 'bg-blue-100 text-blue-800' :
                                evaluation.evaluationType === 'clinical' ? 'bg-green-100 text-green-800' :
                                'bg-purple-100 text-purple-800'
                              }`}>
                                {getTypeLabel(evaluation.evaluationType)}
                              </span>
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getScoreColor(score)}`}>
                                <Star className="h-3 w-3 mr-1" />
                                {score}分
                              </span>
                            </div>

                            <div className="flex items-center space-x-4 text-xs text-gray-500 mb-3">
                              <div className="flex items-center space-x-1">
                                <Calendar className="h-3 w-3" />
                                <span>{format(new Date(evaluation.timestamp), 'yyyy年MM月dd日 HH:mm')}</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <FileText className="h-3 w-3" />
                                <span>{evaluation.content.length} 字符</span>
                              </div>
                            </div>

                            <p className="text-sm text-gray-600 line-clamp-2 leading-relaxed">
                              {evaluation.content.substring(0, 200)}
                              {evaluation.content.length > 200 && '...'}
                            </p>
                          </div>

                          <div className="flex items-center space-x-2 ml-4">
                            <button
                              onClick={() => setExpandedEvaluation(isExpanded ? null : evaluation.id)}
                              className={`inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-lg transition-colors ${
                                isExpanded
                                  ? 'text-blue-700 bg-blue-100 hover:bg-blue-200'
                                  : 'text-gray-700 bg-gray-100 hover:bg-gray-200'
                              }`}
                            >
                              <Eye className="h-3 w-3 mr-1" />
                              {isExpanded ? '收起' : '详情'}
                            </button>

                            <button
                              onClick={() => handleDeleteEvaluation(evaluation.id)}
                              className="inline-flex items-center px-3 py-1.5 text-xs font-medium text-red-700 bg-red-100 rounded-lg hover:bg-red-200 transition-colors"
                              title="删除此评估记录"
                            >
                              <Trash2 className="h-3 w-3" />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Expanded Details */}
                  {isExpanded && evaluation.result && (
                    <div className="mt-6 mx-6 mb-2">
                      <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-6 border border-gray-200">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                          {/* Left Column */}
                          <div className="space-y-6">
                            {/* Categories */}
                            {evaluation.result.categories && (
                              <div>
                                <div className="flex items-center space-x-2 mb-4">
                                  <Target className="h-4 w-4 text-blue-600" />
                                  <h5 className="text-sm font-semibold text-gray-900">分项评分</h5>
                                </div>
                                <div className="space-y-3">
                                  {evaluation.result.categories.map((category, index) => (
                                    <div key={index} className="flex items-center justify-between p-3 bg-white rounded-lg border border-gray-200">
                                      <span className="text-sm font-medium text-gray-700">{category.name}</span>
                                      <div className="flex items-center space-x-2">
                                        <div className="w-16 bg-gray-200 rounded-full h-2">
                                          <div
                                            className={`h-2 rounded-full ${
                                              category.score >= 90 ? 'bg-green-500' :
                                              category.score >= 80 ? 'bg-blue-500' :
                                              category.score >= 70 ? 'bg-yellow-500' : 'bg-red-500'
                                            }`}
                                            style={{ width: `${category.score}%` }}
                                          ></div>
                                        </div>
                                        <span className={`text-sm font-semibold px-2 py-1 rounded ${getScoreColor(category.score)}`}>
                                          {category.score}
                                        </span>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>

                          {/* Right Column */}
                          <div className="space-y-6">
                            {/* Summary */}
                            {evaluation.result.summary && (
                              <div>
                                <div className="flex items-center space-x-2 mb-4">
                                  <FileText className="h-4 w-4 text-green-600" />
                                  <h5 className="text-sm font-semibold text-gray-900">评估总结</h5>
                                </div>
                                <div className="p-4 bg-white rounded-lg border border-gray-200">
                                  <p className="text-sm text-gray-700 leading-relaxed">{evaluation.result.summary}</p>
                                </div>
                              </div>
                            )}

                            {/* Recommendations */}
                            {evaluation.result.recommendations && evaluation.result.recommendations.length > 0 && (
                              <div>
                                <div className="flex items-center space-x-2 mb-4">
                                  <TrendingUp className="h-4 w-4 text-orange-600" />
                                  <h5 className="text-sm font-semibold text-gray-900">改进建议</h5>
                                </div>
                                <div className="space-y-2">
                                  {evaluation.result.recommendations.slice(0, 3).map((rec, index) => (
                                    <div key={index} className="flex items-start p-3 bg-white rounded-lg border border-gray-200">
                                      <div className="flex items-center justify-center w-6 h-6 bg-orange-100 rounded-full mr-3 mt-0.5">
                                        <span className="text-xs font-semibold text-orange-600">{index + 1}</span>
                                      </div>
                                      <p className="text-sm text-gray-700 leading-relaxed">{rec}</p>
                                    </div>
                                  ))}
                                  {evaluation.result.recommendations.length > 3 && (
                                    <div className="text-center">
                                      <span className="text-xs text-gray-500">
                                        还有 {evaluation.result.recommendations.length - 3} 条建议...
                                      </span>
                                    </div>
                                  )}
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )
            })}
          </div>
        ) : (
          <div className="py-16 px-6 text-center">
            <div className="max-w-md mx-auto">
              <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center">
                <FileText className="h-10 w-10 text-gray-400" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                {evaluations.length === 0 ? '暂无评估记录' : '没有找到匹配的记录'}
              </h3>
              <p className="text-gray-600 mb-6 leading-relaxed">
                {evaluations.length === 0
                  ? '您还没有进行过任何病历评估。开始您的第一次评估，建立完整的评估历史记录。'
                  : '当前筛选条件下没有找到匹配的记录。尝试调整搜索关键词或筛选条件。'
                }
              </p>
              {evaluations.length === 0 ? (
                <a
                  href="/evaluator"
                  className="inline-flex items-center px-6 py-3 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
                >
                  <TrendingUp className="h-4 w-4 mr-2" />
                  开始评估
                </a>
              ) : (
                <button
                  onClick={() => {
                    setSearchTerm('')
                    setFilterScore('all')
                    setFilterType('all')
                  }}
                  className="inline-flex items-center px-6 py-3 text-sm font-medium text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
                >
                  <Filter className="h-4 w-4 mr-2" />
                  清除筛选条件
                </button>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Export All Button */}
      {evaluations.length > 0 && (
        <div className="flex justify-center">
          <div className="bg-white rounded-xl border border-gray-200 p-6 shadow-sm">
            <div className="text-center mb-4">
              <h4 className="text-sm font-medium text-gray-900 mb-1">数据导出</h4>
              <p className="text-xs text-gray-500">将所有评估记录导出为 JSON 格式</p>
            </div>
            <button
              onClick={handleExportHistory}
              className="inline-flex items-center px-6 py-3 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
            >
              <Download className="h-4 w-4 mr-2" />
              导出所有记录 ({evaluations.length} 条)
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

export default History
