import React, { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import toast from 'react-hot-toast'
import {
  Settings as SettingsIcon,
  Save,
  TestTube,
  Loader2,
  CheckCircle,
  XCircle,
  Eye,
  EyeOff,
  AlertCircle,
  RefreshCw,
  Trash2,
  MessageSquare,
  Cpu
} from 'lucide-react'
import { testLLMConnection } from '../services/llmService'
import PromptManager from '../components/PromptManager'
import CustomSelect from '../components/CustomSelect'

const Settings = () => {
  const [activeTab, setActiveTab] = useState('llm')
  const [isTestingConnection, setIsTestingConnection] = useState(false)
  const [connectionStatus, setConnectionStatus] = useState(null)
  const [showApiKey, setShowApiKey] = useState(false)
  const [isSaving, setIsSaving] = useState(false)

  const { register, handleSubmit, watch, reset, setValue, formState: { errors, isDirty } } = useForm({
    defaultValues: {
      endpoint: '',
      apiKey: '',
      modelName: 'gpt-3.5-turbo',
      temperature: 0.3,
      maxTokens: 2000,
      timeout: 30000
    }
  })

  useEffect(() => {
    // Load saved configuration
    const savedConfig = JSON.parse(localStorage.getItem('llmConfig') || '{}')
    if (Object.keys(savedConfig).length > 0) {
      reset(savedConfig)
    }
  }, [reset])

  const onSubmit = async (data) => {
    setIsSaving(true)
    try {
      // Save configuration to localStorage
      localStorage.setItem('llmConfig', JSON.stringify(data))
      toast.success('配置已保存')
      setConnectionStatus(null) // Reset connection status when config changes
    } catch (error) {
      toast.error('保存配置失败')
    } finally {
      setIsSaving(false)
    }
  }

  const handleTestConnection = async () => {
    const currentData = watch()
    
    // Save current form data temporarily for testing
    const tempConfig = JSON.parse(localStorage.getItem('llmConfig') || '{}')
    localStorage.setItem('llmConfig', JSON.stringify(currentData))
    
    setIsTestingConnection(true)
    setConnectionStatus(null)
    
    try {
      const result = await testLLMConnection()
      setConnectionStatus(result)
      
      if (result.success) {
        toast.success('连接测试成功')
      } else {
        toast.error(`连接测试失败: ${result.message}`)
      }
    } catch (error) {
      setConnectionStatus({
        success: false,
        message: error.message
      })
      toast.error(`连接测试失败: ${error.message}`)
    } finally {
      setIsTestingConnection(false)
      // Restore original config if test fails
      if (connectionStatus && !connectionStatus.success) {
        localStorage.setItem('llmConfig', JSON.stringify(tempConfig))
      }
    }
  }

  const handleResetConfig = () => {
    if (window.confirm('确定要重置所有配置吗？此操作不可撤销。')) {
      localStorage.removeItem('llmConfig')
      reset({
        endpoint: '',
        apiKey: '',
        modelName: 'gpt-3.5-turbo',
        temperature: 0.3,
        maxTokens: 2000,
        timeout: 30000
      })
      setConnectionStatus(null)
      toast.success('配置已重置')
    }
  }

  const commonEndpoints = [
    { name: 'OpenAI API', url: 'https://api.openai.com/v1/chat/completions' },
    { name: 'Google Gemini', url: 'https://generativelanguage.googleapis.com/v1/models/gemini-pro:generateContent' },
    { name: 'Azure OpenAI', url: 'https://your-resource.openai.azure.com/openai/deployments/your-deployment/chat/completions?api-version=2023-05-15' },
    { name: '本地部署', url: 'http://localhost:8000/v1/chat/completions' },
    { name: '自定义端点', url: '' }
  ]

  const commonModels = [
    'gpt-4',
    'gpt-4-turbo-preview',
    'gpt-3.5-turbo',
    'gpt-3.5-turbo-16k',
    'gemini-pro',
    'gemini-pro-vision',
    'claude-3-opus',
    'claude-3-sonnet',
    'claude-3-haiku'
  ]

  const tabs = [
    {
      id: 'llm',
      name: 'LLM 配置',
      icon: Cpu,
      description: '大型语言模型连接参数'
    },
    {
      id: 'prompts',
      name: '提示词管理',
      icon: MessageSquare,
      description: '自定义评估提示词'
    }
  ]

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <div className="card p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">系统设置</h1>
            <p className="mt-1 text-gray-600">
              配置系统参数，管理评估提示词，确保病历评估功能正常运行
            </p>
          </div>
          <SettingsIcon className="h-8 w-8 text-gray-400" />
        </div>
      </div>

      {/* Tabs */}
      <div className="card">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap flex items-center space-x-2 ${
                    activeTab === tab.id
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{tab.name}</span>
                </button>
              )
            })}
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'llm' && (
            <div>
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900">LLM 模型配置</h3>
                <p className="text-sm text-gray-600 mt-1">
                  配置大型语言模型连接参数，确保病历评估功能正常运行
                </p>
              </div>

              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">

                {/* Endpoint Configuration */}
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      API 端点 <span className="text-danger-500">*</span>
                    </label>
                    <div className="mb-2">
                      <CustomSelect
                        options={commonEndpoints.map(endpoint => ({
                          value: endpoint.name,
                          label: endpoint.name,
                          description: endpoint.url || '自定义端点'
                        }))}
                        value=""
                        onChange={(value) => {
                          const selected = commonEndpoints.find(ep => ep.name === value)
                          if (selected && selected.url) {
                            reset({ ...watch(), endpoint: selected.url })
                          }
                        }}
                        placeholder="选择常用端点"
                      />
                    </div>
                    <input
                      type="url"
                      {...register('endpoint', {
                        required: 'API端点不能为空',
                        pattern: {
                          value: /^https?:\/\/.+/,
                          message: '请输入有效的HTTP/HTTPS URL'
                        }
                      })}
                      placeholder="https://api.openai.com/v1/chat/completions"
                      className="input"
                    />
                    {errors.endpoint && (
                      <p className="mt-1 text-sm text-danger-600 flex items-center">
                        <AlertCircle className="h-4 w-4 mr-1" />
                        {errors.endpoint.message}
                      </p>
                    )}
                  </div>

                  {/* API Key */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      API 密钥 <span className="text-danger-500">*</span>
                    </label>
                    <div className="relative">
                      <input
                        type={showApiKey ? 'text' : 'password'}
                        {...register('apiKey', { required: 'API密钥不能为空' })}
                        placeholder="sk-..."
                        className="input pr-10"
                      />
                      <button
                        type="button"
                        onClick={() => setShowApiKey(!showApiKey)}
                        className="absolute inset-y-0 right-0 pr-3 flex items-center"
                      >
                        {showApiKey ? (
                          <EyeOff className="h-4 w-4 text-gray-400" />
                        ) : (
                          <Eye className="h-4 w-4 text-gray-400" />
                        )}
                      </button>
                    </div>
                    {errors.apiKey && (
                      <p className="mt-1 text-sm text-danger-600 flex items-center">
                        <AlertCircle className="h-4 w-4 mr-1" />
                        {errors.apiKey.message}
                      </p>
                    )}
                  </div>

                  {/* Model Selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      模型名称 <span className="text-danger-500">*</span>
                    </label>
                    <div className="mb-2">
                      <CustomSelect
                        options={commonModels.map(model => ({
                          value: model,
                          label: model,
                          description: model.includes('gpt-4') ? '高性能模型' :
                                  model.includes('claude') ? 'Anthropic模型' :
                                  model.includes('gemini') ? 'Google模型' : '标准模型'
                        }))}
                        value=""
                        onChange={(value) => {
                          setValue('modelName', value)
                        }}
                        placeholder="选择常用模型（可选）"
                        searchable={false}
                      />
                    </div>
                    <input
                      type="text"
                      {...register('modelName', {
                        required: '模型名称不能为空'
                      })}
                      placeholder="输入模型名称，如：gpt-4, claude-3-opus, gemini-pro 等"
                      className="input"
                    />
                    {errors.modelName && (
                      <p className="mt-1 text-sm text-danger-600">{errors.modelName.message}</p>
                    )}
                    <p className="mt-1 text-xs text-gray-500">
                      请输入您要使用的模型名称，或从上方快速选择常用模型
                    </p>
                  </div>
                </div>

                {/* Advanced Settings */}
                <div className="card p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-6">高级设置</h3>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Temperature (0-1)
                      </label>
                      <input
                        type="number"
                        step="0.1"
                        min="0"
                        max="1"
                        {...register('temperature', {
                          valueAsNumber: true,
                          min: { value: 0, message: '最小值为0' },
                          max: { value: 1, message: '最大值为1' }
                        })}
                        className="input"
                      />
                      <p className="mt-1 text-xs text-gray-500">控制输出的随机性</p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        最大令牌数
                      </label>
                      <input
                        type="number"
                        min="100"
                        max="4000"
                        {...register('maxTokens', {
                          valueAsNumber: true,
                          min: { value: 100, message: '最小值为100' },
                          max: { value: 4000, message: '最大值为4000' }
                        })}
                        className="input"
                      />
                      <p className="mt-1 text-xs text-gray-500">限制响应长度</p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        超时时间 (毫秒)
                      </label>
                      <input
                        type="number"
                        min="5000"
                        max="60000"
                        step="1000"
                        {...register('timeout', {
                          valueAsNumber: true,
                          min: { value: 5000, message: '最小值为5秒' },
                          max: { value: 60000, message: '最大值为60秒' }
                        })}
                        className="input"
                      />
                      <p className="mt-1 text-xs text-gray-500">API请求超时时间</p>
                    </div>
                  </div>
                </div>

                {/* Connection Test */}
                <div className="card p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">连接测试</h3>

                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      {connectionStatus && (
                        <div className={`flex items-center p-3 rounded-lg ${
                          connectionStatus.success
                            ? 'bg-success-50 text-success-700'
                            : 'bg-danger-50 text-danger-700'
                        }`}>
                          {connectionStatus.success ? (
                            <CheckCircle className="h-5 w-5 mr-2" />
                          ) : (
                            <XCircle className="h-5 w-5 mr-2" />
                          )}
                          <div>
                            <p className="font-medium">
                              {connectionStatus.success ? '连接成功' : '连接失败'}
                            </p>
                            <p className="text-sm">{connectionStatus.message}</p>
                            {connectionStatus.model && (
                              <p className="text-xs">模型: {connectionStatus.model}</p>
                            )}
                          </div>
                        </div>
                      )}
                    </div>

                    <button
                      type="button"
                      onClick={handleTestConnection}
                      disabled={isTestingConnection}
                      className="btn-outline ml-4"
                    >
                      {isTestingConnection ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          测试中...
                        </>
                      ) : (
                        <>
                          <TestTube className="h-4 w-4 mr-2" />
                          测试连接
                        </>
                      )}
                    </button>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex justify-between items-center">
                  <button
                    type="button"
                    onClick={handleResetConfig}
                    className="btn-outline text-red-600 border-red-300 hover:bg-red-50"
                  >
                    <Trash2 className="h-4 w-4" />
                    重置配置
                  </button>

                  <div className="flex items-center space-x-3">
                    <button
                      type="button"
                      onClick={() => reset()}
                      disabled={!isDirty}
                      className="btn-secondary"
                    >
                      <RefreshCw className="h-4 w-4" />
                      撤销更改
                    </button>

                    <button
                      type="submit"
                      disabled={isSaving}
                      className="btn-primary"
                    >
                      {isSaving ? (
                        <>
                          <Loader2 className="h-4 w-4 animate-spin" />
                          保存中...
                        </>
                      ) : (
                        <>
                          <Save className="h-4 w-4" />
                          保存配置
                        </>
                      )}
                    </button>
                  </div>
                </div>
              </form>

              {/* Help Section */}
              <div className="mt-6 space-y-4">
                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <h4 className="text-sm font-semibold text-blue-900 mb-2">配置说明</h4>
                  <div className="space-y-1 text-xs text-blue-800">
                    <p>• <strong>API端点</strong>: 大型语言模型的API地址，支持OpenAI、Google Gemini、Azure OpenAI等</p>
                    <p>• <strong>API密钥</strong>: 用于身份验证的密钥，请确保密钥有效且有足够的配额</p>
                    <p>• <strong>模型名称</strong>: 要使用的具体模型，不同模型有不同的能力和成本</p>
                    <p>• <strong>Temperature</strong>: 控制输出的创造性，0为最确定性，1为最随机</p>
                    <p>• <strong>Google Gemini</strong>: 需要在Google AI Studio获取API密钥，模型推荐使用gemini-pro</p>
                    <p>• 配置完成后请先测试连接，确保可以正常访问API服务</p>
                  </div>
                </div>

                <div className="p-4 bg-amber-50 border border-amber-200 rounded-lg">
                  <h4 className="text-sm font-semibold text-amber-900 mb-2">网络问题解决方案</h4>
                  <div className="space-y-1 text-xs text-amber-800">
                    <p>• <strong>网络错误</strong>: 如果遇到网络连接失败，系统会自动使用本地模拟评估</p>
                    <p>• <strong>CORS问题</strong>: 浏览器可能阻止跨域请求，建议使用本地代理或服务器部署</p>
                    <p>• <strong>防火墙</strong>: 企业网络可能阻止外部API调用，请联系网络管理员</p>
                    <p>• <strong>无配置模式</strong>: 不配置API也可以正常使用，系统提供智能模拟评估功能</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'prompts' && (
            <PromptManager />
          )}
        </div>
      </div>
    </div>
  )
}

export default Settings
