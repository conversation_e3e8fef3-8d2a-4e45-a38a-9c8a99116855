import React, { useEffect } from 'react'
import { generateMockEvaluations, clearMockData } from '../utils/mockHistoryData'
import History from './History'

const HistoryDemo = () => {
  useEffect(() => {
    // Generate mock data when component mounts
    generateMockEvaluations()
    
    // Cleanup function to clear mock data when component unmounts
    return () => {
      // Uncomment the line below if you want to clear data on unmount
      // clearMockData()
    }
  }, [])

  const handleGenerateMockData = () => {
    generateMockEvaluations()
    window.location.reload() // Reload to show the new data
  }

  const handleClearData = () => {
    clearMockData()
    window.location.reload() // Reload to show empty state
  }

  return (
    <div className="space-y-6">
      {/* Demo Controls */}
      <div className="card p-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold text-gray-900">历史记录演示</h2>
            <p className="text-sm text-gray-600">测试优化后的筛选下拉框功能</p>
          </div>
          <div className="flex space-x-3">
            <button
              onClick={handleGenerateMockData}
              className="btn-primary text-sm"
            >
              生成测试数据
            </button>
            <button
              onClick={handleClearData}
              className="btn-outline text-sm"
            >
              清除数据
            </button>
          </div>
        </div>
      </div>

      {/* History Component */}
      <History />
    </div>
  )
}

export default HistoryDemo
