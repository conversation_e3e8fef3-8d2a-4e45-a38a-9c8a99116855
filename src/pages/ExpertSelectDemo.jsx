import React, { useState } from 'react'
import { UserCheck, Heart, Brain, Activity, Stethoscope, FileText } from 'lucide-react'
import CustomSelect from '../components/CustomSelect'

const ExpertSelectDemo = () => {
  const [selectedExpert, setSelectedExpert] = useState('comprehensive')

  // 模拟专家数据
  const mockExperts = [
    {
      id: 'comprehensive',
      name: '综合评估',
      description: '全面评估病历的完整性、准确性、规范性和逻辑性',
      category: '综合',
      isDefault: true
    },
    {
      id: 'clinical',
      name: '临床质量',
      description: '重点评估临床决策的准确性和合理性',
      category: '专科',
      isDefault: true
    },
    {
      id: 'documentation',
      name: '文档规范',
      description: '评估病历书写的规范性和标准化程度',
      category: '综合',
      isDefault: true
    },
    {
      id: 'cardiology',
      name: '心血管专科',
      description: '专注心血管疾病的诊疗质量评估',
      category: '专科',
      isDefault: false
    },
    {
      id: 'neurology',
      name: '神经科专科',
      description: '专注神经系统疾病的诊疗质量评估',
      category: '专科',
      isDefault: false
    }
  ]

  const getExpertIcon = (expert) => {
    if (expert.category === '专科') {
      if (expert.name.includes('心血管') || expert.name.includes('心脏')) {
        return Heart
      } else if (expert.name.includes('神经') || expert.name.includes('脑')) {
        return Brain
      } else if (expert.name.includes('呼吸') || expert.name.includes('肺')) {
        return Activity
      } else {
        return Stethoscope
      }
    } else if (expert.category === '综合') {
      return UserCheck
    } else {
      return FileText
    }
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="card p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">专家选择组件演示</h1>
        <p className="text-gray-600">
          展示优化后的专家选择下拉组件，已移除搜索功能并简化选项显示
        </p>
      </div>

      {/* Demo Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Expert Selection */}
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">设置评审专家</h3>
          
          <CustomSelect
            options={mockExperts.map(expert => ({
              value: expert.id,
              label: expert.name,
              description: expert.description,
              category: expert.category,
              icon: getExpertIcon(expert),
              isDefault: expert.isDefault
            }))}
            value={selectedExpert}
            onChange={setSelectedExpert}
            placeholder="选择评审专家类型"
            searchable={false}
            optionRender={(option) => (
              <div className="flex items-center space-x-3">
                <option.icon className={`h-4 w-4 ${
                  option.category === '综合' ? 'text-blue-600' :
                  option.category === '专科' ? 'text-green-600' :
                  'text-gray-500'
                }`} />
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-gray-900">{option.label}</span>
                    {option.isDefault && (
                      <span className="px-2 py-0.5 text-xs bg-blue-100 text-blue-700 rounded-full">
                        系统专家
                      </span>
                    )}
                  </div>
                  <div className="text-sm text-gray-500 mt-0.5">{option.description}</div>
                </div>
              </div>
            )}
          />
        </div>

        {/* Selected Expert Info */}
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">当前选择</h3>
          
          {selectedExpert && (() => {
            const expert = mockExperts.find(e => e.id === selectedExpert)
            const ExpertIcon = getExpertIcon(expert)
            
            return (
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-200">
                <div className="flex items-center space-x-3 mb-3">
                  <ExpertIcon className="h-6 w-6 text-blue-600" />
                  <div>
                    <h4 className="font-semibold text-blue-900">{expert.name}</h4>
                    <div className="flex items-center space-x-2 mt-1">
                      <span className="text-xs text-blue-600">{expert.category}</span>
                      {expert.isDefault && (
                        <span className="px-2 py-0.5 text-xs bg-blue-100 text-blue-700 rounded-full">
                          系统专家
                        </span>
                      )}
                    </div>
                  </div>
                </div>
                <p className="text-sm text-blue-700">{expert.description}</p>
              </div>
            )
          })()}
        </div>
      </div>

      {/* Optimization Notes */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">优化说明</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <h4 className="font-medium text-gray-900">✅ 已优化</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• 移除了搜索功能 (searchable=false)</li>
              <li>• 简化选项显示，去除重复标签</li>
              <li>• 优化图标分配逻辑</li>
              <li>• 只在系统专家显示徽章</li>
              <li>• 使用颜色编码区分类别</li>
            </ul>
          </div>
          <div className="space-y-3">
            <h4 className="font-medium text-gray-900">🎨 设计改进</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• 更清晰的视觉层次</li>
              <li>• 减少视觉噪音</li>
              <li>• 统一的图标使用</li>
              <li>• 更好的信息密度</li>
              <li>• 专业的医疗场景适配</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ExpertSelectDemo
