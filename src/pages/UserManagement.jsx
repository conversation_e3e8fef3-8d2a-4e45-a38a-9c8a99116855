import React, { useState } from 'react'
import {
  Users,
  Plus,
  Edit,
  Trash2,
  Shield,
  Stethoscope,
  UserCheck,
  Search,
  Filter,
  Settings,
  Heart,
  Activity
} from 'lucide-react'
import toast from 'react-hot-toast'
import authService from '../services/authService'
import CustomSelect from '../components/CustomSelect'

const UserManagement = () => {
  const [users, setUsers] = useState(authService.users.map(user => ({ ...user, password: '******' })))
  const [searchTerm, setSearchTerm] = useState('')
  const [filterRole, setFilterRole] = useState('all')

  // 角色选项配置
  const roleOptions = [
    {
      value: 'all',
      label: '所有角色',
      description: '显示所有用户角色',
      icon: Users,
      color: 'text-gray-600',
      bgColor: 'bg-gray-100',
      borderColor: 'border-gray-200'
    },
    {
      value: 'admin',
      label: '系统管理员',
      description: '完整系统权限，用户管理，系统设置',
      icon: Shield,
      color: 'text-red-700',
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200',
      badge: '最高权限'
    },
    {
      value: 'doctor',
      label: '医生',
      description: '病历评估，诊疗记录，患者管理',
      icon: Stethoscope,
      color: 'text-blue-700',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
      badge: '医疗权限'
    },
    {
      value: 'nurse',
      label: '护士',
      description: '查看评估结果，护理记录',
      icon: UserCheck,
      color: 'text-green-700',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200',
      badge: '护理权限'
    }
  ]

  const getRoleIcon = (role) => {
    switch (role) {
      case 'admin': return Shield
      case 'doctor': return Stethoscope
      case 'nurse': return UserCheck
      default: return Users
    }
  }

  const getRoleLabel = (role) => {
    switch (role) {
      case 'admin': return '系统管理员'
      case 'doctor': return '医生'
      case 'nurse': return '护士'
      default: return '未知'
    }
  }

  const getRoleColor = (role) => {
    switch (role) {
      case 'admin': return 'bg-red-50 text-red-700 border-red-200'
      case 'doctor': return 'bg-blue-50 text-blue-700 border-blue-200'
      case 'nurse': return 'bg-green-50 text-green-700 border-green-200'
      default: return 'bg-gray-50 text-gray-700 border-gray-200'
    }
  }

  const getRolePermissions = (role) => {
    switch (role) {
      case 'admin': return '系统管理、用户管理、完整权限'
      case 'doctor': return '病历评估、诊疗记录、患者管理'
      case 'nurse': return '查看评估、护理记录'
      default: return '基础权限'
    }
  }

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.department.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesRole = filterRole === 'all' || user.role === filterRole
    return matchesSearch && matchesRole
  })

  const handleDeleteUser = (userId) => {
    if (window.confirm('确定要删除这个用户吗？')) {
      setUsers(users.filter(user => user.id !== userId))
      toast.success('用户删除成功')
    }
  }

  // 角色选择的自定义渲染
  const renderRoleOption = (option) => (
    <div className={`role-option role-${option.value} flex items-center space-x-3 p-3 rounded-lg border-2 transition-all duration-200`}>
      <div className={`flex items-center justify-center w-10 h-10 rounded-full ${option.bgColor} ${option.borderColor} border-2 shadow-sm`}>
        <option.icon className={`role-icon h-5 w-5 ${option.color}`} />
      </div>
      <div className="flex-1 min-w-0">
        <div className={`font-semibold ${option.color} truncate text-sm`}>
          {option.label}
        </div>
        <div className="text-xs text-gray-600 truncate mt-0.5 leading-relaxed">
          {option.description}
        </div>
      </div>
      {option.badge && (
        <span className={`role-badge px-3 py-1 text-xs font-semibold rounded-full ${option.bgColor} ${option.color} border-2 ${option.borderColor} shadow-sm`}>
          {option.badge}
        </span>
      )}
    </div>
  )

  // 角色选择的值渲染
  const renderRoleValue = (option) => (
    <div className="flex items-center space-x-3">
      <div className={`flex items-center justify-center w-6 h-6 rounded-full ${option.bgColor} ${option.borderColor} border`}>
        <option.icon className={`h-3.5 w-3.5 ${option.color}`} />
      </div>
      <span className={`font-semibold ${option.color} text-sm`}>{option.label}</span>
      {option.badge && option.value !== 'all' && (
        <span className={`px-2 py-0.5 text-xs font-medium rounded-full ${option.bgColor} ${option.color} border ${option.borderColor}`}>
          {option.badge}
        </span>
      )}
    </div>
  )

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="card p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">用户管理</h1>
            <p className="mt-1 text-gray-600">
              管理系统用户账户和权限
            </p>
          </div>
          <button className="btn-primary">
            <Plus className="h-4 w-4 mr-2" />
            添加用户
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="card p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="搜索用户名、姓名或科室..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="input pl-10"
            />
          </div>

          {/* Role Filter */}
          <div className="relative">
            <CustomSelect
              options={roleOptions}
              value={filterRole}
              onChange={(value) => setFilterRole(value)}
              placeholder="选择角色筛选..."
              optionRender={renderRoleOption}
              valueRender={renderRoleValue}
              searchable={false}
              className="w-full"
            />
          </div>
        </div>
      </div>

      {/* Users List */}
      <div className="card">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            用户列表 ({filteredUsers.length})
          </h3>
        </div>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  用户
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  角色
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  科室
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  状态
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  操作
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredUsers.map((user) => {
                const RoleIcon = getRoleIcon(user.role)
                return (
                  <tr key={user.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="h-10 w-10 bg-gray-200 rounded-full flex items-center justify-center">
                          <RoleIcon className="h-5 w-5 text-gray-600" />
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">{user.name}</div>
                          <div className="text-sm text-gray-500">@{user.username}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex flex-col space-y-1">
                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold border ${getRoleColor(user.role)}`}>
                          <RoleIcon className="h-3 w-3 mr-1.5" />
                          {getRoleLabel(user.role)}
                        </span>
                        <span className="text-xs text-gray-500 truncate max-w-32">
                          {getRolePermissions(user.role)}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {user.department}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        活跃
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-2">
                        <button className="text-blue-600 hover:text-blue-900">
                          <Edit className="h-4 w-4" />
                        </button>
                        <button 
                          onClick={() => handleDeleteUser(user.id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                )
              })}
            </tbody>
          </table>
        </div>

        {filteredUsers.length === 0 && (
          <div className="text-center py-12">
            <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">没有找到用户</h3>
            <p className="text-gray-600">尝试调整搜索条件或筛选器</p>
          </div>
        )}
      </div>
    </div>
  )
}

export default UserManagement
