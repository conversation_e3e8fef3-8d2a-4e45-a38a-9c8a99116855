import React from 'react'
import { useAuth } from '../contexts/AuthContext'
import { 
  Shield, 
  Stethoscope, 
  UserCheck, 
  Key, 
  Users,
  Settings,
  Lock,
  CheckCircle
} from 'lucide-react'

const AuthDemo = () => {
  const { user, hasPermission, logout } = useAuth()

  const permissions = [
    { key: 'read', label: '查看权限', icon: CheckCircle },
    { key: 'write', label: '编辑权限', icon: CheckCircle },
    { key: 'evaluate', label: '评估权限', icon: CheckCircle },
    { key: 'delete', label: '删除权限', icon: CheckCircle },
    { key: 'manage_users', label: '用户管理', icon: Users },
    { key: 'manage_settings', label: '系统设置', icon: Settings }
  ]

  const getRoleInfo = (role) => {
    switch (role) {
      case 'admin':
        return {
          name: '系统管理员',
          icon: Shield,
          color: 'text-red-600',
          bgColor: 'bg-red-100',
          description: '拥有系统的完全访问权限'
        }
      case 'doctor':
        return {
          name: '医生',
          icon: Stethoscope,
          color: 'text-blue-600',
          bgColor: 'bg-blue-100',
          description: '可以进行病历评估和查看历史记录'
        }
      case 'nurse':
        return {
          name: '护士',
          icon: UserCheck,
          color: 'text-green-600',
          bgColor: 'bg-green-100',
          description: '可以查看和进行基础评估'
        }
      default:
        return {
          name: '未知角色',
          icon: UserCheck,
          color: 'text-gray-600',
          bgColor: 'bg-gray-100',
          description: '角色信息不明确'
        }
    }
  }

  const roleInfo = getRoleInfo(user?.role)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="card p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">认证系统演示</h1>
        <p className="text-gray-600">
          展示登录功能和权限管理系统
        </p>
      </div>

      {/* Current User Info */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">当前用户信息</h3>
        
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200">
          <div className="flex items-center space-x-4">
            <div className={`h-16 w-16 ${roleInfo.bgColor} rounded-full flex items-center justify-center`}>
              <roleInfo.icon className={`h-8 w-8 ${roleInfo.color}`} />
            </div>
            <div className="flex-1">
              <h4 className="text-xl font-semibold text-gray-900">{user?.name}</h4>
              <p className="text-sm text-gray-600 mb-1">@{user?.username}</p>
              <div className="flex items-center space-x-2">
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${roleInfo.bgColor} ${roleInfo.color}`}>
                  <roleInfo.icon className="h-4 w-4 mr-1" />
                  {roleInfo.name}
                </span>
                <span className="text-sm text-gray-500">• {user?.department}</span>
              </div>
              <p className="text-sm text-gray-600 mt-2">{roleInfo.description}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Permissions */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">权限列表</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {permissions.map((permission) => {
            const hasAccess = hasPermission(permission.key)
            return (
              <div
                key={permission.key}
                className={`p-4 rounded-lg border-2 ${
                  hasAccess 
                    ? 'border-green-200 bg-green-50' 
                    : 'border-gray-200 bg-gray-50'
                }`}
              >
                <div className="flex items-center space-x-3">
                  <permission.icon className={`h-5 w-5 ${
                    hasAccess ? 'text-green-600' : 'text-gray-400'
                  }`} />
                  <div className="flex-1">
                    <div className={`font-medium ${
                      hasAccess ? 'text-green-900' : 'text-gray-500'
                    }`}>
                      {permission.label}
                    </div>
                    <div className={`text-sm ${
                      hasAccess ? 'text-green-600' : 'text-gray-400'
                    }`}>
                      {hasAccess ? '已授权' : '无权限'}
                    </div>
                  </div>
                  {hasAccess && (
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  )}
                </div>
              </div>
            )
          })}
        </div>
      </div>

      {/* Demo Accounts */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">演示账号说明</h3>
        
        <div className="space-y-4">
          <div className="p-4 border border-red-200 rounded-lg bg-red-50">
            <div className="flex items-center space-x-3 mb-2">
              <Shield className="h-5 w-5 text-red-600" />
              <span className="font-medium text-red-900">系统管理员 (admin/admin123)</span>
            </div>
            <p className="text-sm text-red-700">拥有所有权限，可以管理用户和系统设置</p>
          </div>
          
          <div className="p-4 border border-blue-200 rounded-lg bg-blue-50">
            <div className="flex items-center space-x-3 mb-2">
              <Stethoscope className="h-5 w-5 text-blue-600" />
              <span className="font-medium text-blue-900">医生 (doctor/doctor123)</span>
            </div>
            <p className="text-sm text-blue-700">可以进行病历评估、查看历史记录和编辑内容</p>
          </div>
          
          <div className="p-4 border border-green-200 rounded-lg bg-green-50">
            <div className="flex items-center space-x-3 mb-2">
              <UserCheck className="h-5 w-5 text-green-600" />
              <span className="font-medium text-green-900">护士 (nurse/nurse123)</span>
            </div>
            <p className="text-sm text-green-700">可以查看内容和进行基础评估</p>
          </div>
        </div>
      </div>

      {/* Logout */}
      <div className="card p-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">会话管理</h3>
            <p className="text-sm text-gray-600">管理您的登录会话</p>
          </div>
          <button
            onClick={logout}
            className="btn-outline text-red-600 border-red-300 hover:bg-red-50"
          >
            <Lock className="h-4 w-4 mr-2" />
            登出系统
          </button>
        </div>
      </div>
    </div>
  )
}

export default AuthDemo
