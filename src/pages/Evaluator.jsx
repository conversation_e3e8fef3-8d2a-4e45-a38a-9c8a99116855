import React, { useState, useRef, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { useNavigate } from 'react-router-dom'
import toast from 'react-hot-toast'
import {
  FileText,
  Send,
  Loader2,
  AlertCircle,
  CheckCircle,
  Copy,
  Download,
  RefreshCw,
  ChevronDown,
  Eye,
  Tag,
  UserCheck,
  Stethoscope,
  Brain,
  Heart,
  Activity
} from 'lucide-react'
import EvaluationResult from '../components/EvaluationResult'
import CustomSelect from '../components/CustomSelect'
import { evaluateMedicalRecord } from '../services/llmService'
import { getAllPrompts } from '../services/promptService'

const Evaluator = () => {
  const navigate = useNavigate()
  const [isEvaluating, setIsEvaluating] = useState(false)
  const [evaluationResult, setEvaluationResult] = useState(null)
  const [wordCount, setWordCount] = useState(0)
  const [prompts, setPrompts] = useState([])
  const [selectedPrompt, setSelectedPrompt] = useState(null)
  const [showPromptPreview, setShowPromptPreview] = useState(false)
  const textareaRef = useRef(null)

  const { register, handleSubmit, watch, reset, setValue, formState: { errors } } = useForm({
    defaultValues: {
      medicalRecord: '',
      patientInfo: '',
      promptId: 'comprehensive'
    }
  })

  const medicalRecord = watch('medicalRecord')
  const promptId = watch('promptId')

  useEffect(() => {
    // 加载提示词列表
    const allPrompts = getAllPrompts()
    setPrompts(allPrompts)

    // 设置默认选中的提示词
    const defaultPrompt = allPrompts.find(p => p.id === 'comprehensive')
    setSelectedPrompt(defaultPrompt)
    setValue('promptId', 'comprehensive')
  }, [])

  useEffect(() => {
    setWordCount(medicalRecord?.length || 0)
  }, [medicalRecord])

  useEffect(() => {
    // 当选择的提示词ID改变时，更新选中的提示词对象
    const prompt = prompts.find(p => p.id === promptId)
    setSelectedPrompt(prompt)
  }, [promptId, prompts])

  const onSubmit = async (data) => {
    if (!data.medicalRecord.trim()) {
      toast.error('请输入病历内容')
      return
    }

    if (!selectedPrompt) {
      toast.error('请选择评估提示词')
      return
    }

    setIsEvaluating(true)
    
    try {
      // Check if LLM is configured
      const config = JSON.parse(localStorage.getItem('llmConfig') || '{}')
      const isUsingMockMode = !config.endpoint || !config.apiKey

      const result = await evaluateMedicalRecord({
        content: data.medicalRecord,
        patientInfo: data.patientInfo,
        evaluationType: data.promptId,
        customPrompt: selectedPrompt
      })

      setEvaluationResult(result)

      // Show appropriate success message
      if (isUsingMockMode) {
        toast.success('评估完成（使用本地模拟模式）')
      } else {
        toast.success('评估完成')
      }

      // Save to history
      const history = JSON.parse(localStorage.getItem('evaluationHistory') || '[]')
      const newEvaluation = {
        id: Date.now().toString(),
        timestamp: new Date().toISOString(),
        content: data.medicalRecord,
        patientInfo: data.patientInfo,
        evaluationType: data.promptId,
        promptName: selectedPrompt.name,
        result: result,
        overallScore: result.overallScore
      }
      history.push(newEvaluation)
      localStorage.setItem('evaluationHistory', JSON.stringify(history))

      toast.success('病历评估完成')
    } catch (error) {
      console.error('Evaluation error:', error)
      toast.error(error.message || '评估失败，请检查网络连接和模型配置')
    } finally {
      setIsEvaluating(false)
    }
  }

  const handleClearForm = () => {
    reset()
    setEvaluationResult(null)
    setWordCount(0)
  }

  const handlePasteExample = () => {
    const exampleRecord = `患者姓名：张某某
性别：男
年龄：45岁
主诉：胸痛3小时
现病史：患者3小时前无明显诱因出现胸骨后疼痛，呈压榨性，向左肩背部放射，伴出汗、恶心，无发热、咳嗽。既往有高血压病史5年，规律服用降压药物。
体格检查：T 36.8℃，P 95次/分，R 20次/分，BP 150/90mmHg。神志清楚，精神可，心界不大，心率95次/分，律齐，各瓣膜听诊区未闻及病理性杂音。双肺呼吸音清，未闻及干湿性啰音。腹部平软，无压痛。
辅助检查：心电图示：V1-V4导联ST段抬高。
诊断：急性前壁心肌梗死
处理：给予阿司匹林、氯吡格雷双联抗血小板，阿托伐他汀调脂，美托洛尔控制心率，建议急诊PCI治疗。`
    
    reset({ medicalRecord: exampleRecord })
    toast.success('已填入示例病历')
  }

  // Check if LLM is configured
  const config = JSON.parse(localStorage.getItem('llmConfig') || '{}')
  const isLLMConfigured = config.endpoint && config.apiKey

  return (
    <div className="max-w-7xl mx-auto space-y-6">
      {/* Status Indicator */}
      {!isLLMConfigured && (
        <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-amber-600 mr-3" />
            <div className="flex-1">
              <p className="text-sm font-medium text-amber-800">
                当前使用本地模拟评估模式
              </p>
              <p className="text-xs text-amber-700 mt-1">
                如需使用真实AI模型，请前往
                <button
                  onClick={() => navigate('/settings')}
                  className="text-amber-800 underline hover:text-amber-900 mx-1"
                >
                  设置页面
                </button>
                配置API连接
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Header */}
      <div className="card p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">病历评估</h1>
            <p className="mt-1 text-gray-600">
              使用AI技术对病历进行全面评估，提供专业的质量分析和改进建议
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              type="button"
              onClick={handlePasteExample}
              className="btn-outline"
            >
              <FileText className="h-4 w-4" />
              示例病历
            </button>
            <button
              type="button"
              onClick={handleClearForm}
              className="btn-secondary"
            >
              <RefreshCw className="h-4 w-4" />
              清空重置
            </button>
          </div>
        </div>
      </div>

      {/* Patient Info and Expert Selection */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Patient Info */}
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">患者信息（可选）</h3>
          <textarea
            {...register('patientInfo')}
            placeholder="请输入患者基本信息，如：姓名、年龄、性别、病史等..."
            className="textarea h-24 w-full"
          />
        </div>

        {/* Expert Selection */}
        <div className="card p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">设置评审专家</h3>
            {selectedPrompt && (
              <button
                type="button"
                onClick={() => setShowPromptPreview(!showPromptPreview)}
                className="text-sm text-primary-600 hover:text-primary-700 flex items-center"
              >
                <Eye className="h-4 w-4 mr-1" />
                {showPromptPreview ? '隐藏预览' : '预览内容'}
              </button>
            )}
          </div>

          <div className="space-y-4">
            <CustomSelect
              options={prompts.map(prompt => {
                // 根据不同类型分配不同图标，避免重复
                let icon = UserCheck
                if (prompt.category === '专科') {
                  if (prompt.name.includes('心血管') || prompt.name.includes('心脏')) {
                    icon = Heart
                  } else if (prompt.name.includes('神经') || prompt.name.includes('脑')) {
                    icon = Brain
                  } else if (prompt.name.includes('呼吸') || prompt.name.includes('肺')) {
                    icon = Activity
                  } else {
                    icon = Stethoscope
                  }
                } else if (prompt.category === '综合') {
                  icon = UserCheck
                } else {
                  icon = FileText
                }

                return {
                  value: prompt.id,
                  label: prompt.name,
                  description: prompt.description,
                  category: prompt.category,
                  icon: icon,
                  isDefault: prompt.isDefault
                }
              })}
              value={promptId}
              onChange={(value) => setValue('promptId', value)}
              placeholder="选择评审专家类型"
              searchable={false}
              optionRender={(option) => (
                <div className="flex items-center space-x-3">
                  <option.icon className={`h-4 w-4 ${
                    option.category === '综合' ? 'text-blue-600' :
                    option.category === '专科' ? 'text-green-600' :
                    'text-gray-500'
                  }`} />
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-gray-900">{option.label}</span>
                      {option.isDefault && (
                        <span className="px-2 py-0.5 text-xs bg-blue-100 text-blue-700 rounded-full">
                          系统专家
                        </span>
                      )}
                    </div>
                    <div className="text-sm text-gray-500 mt-0.5">{option.description}</div>
                  </div>
                </div>
              )}
            />

            {selectedPrompt && (
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-200">
                <div className="flex items-center space-x-2 mb-2">
                  <UserCheck className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium text-blue-900">当前专家：{selectedPrompt.name}</span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    selectedPrompt.isDefault
                      ? 'bg-blue-100 text-blue-700'
                      : 'bg-green-100 text-green-700'
                  }`}>
                    {selectedPrompt.isDefault ? '系统专家' : '自定义专家'}
                  </span>
                </div>
                <p className="text-sm text-blue-700">{selectedPrompt.description}</p>

                {showPromptPreview && (
                  <div className="mt-3 p-3 bg-white rounded border border-blue-200">
                    <h4 className="text-xs font-medium text-gray-700 mb-2">专家评审标准预览：</h4>
                    <pre className="text-xs text-gray-600 whitespace-pre-wrap max-h-32 overflow-y-auto">
                      {selectedPrompt.content.substring(0, 500)}
                      {selectedPrompt.content.length > 500 && '...'}
                    </pre>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Input Section */}
        <div className="space-y-6">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">

            {/* Medical Record Input */}
            <div className="card p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">病历内容</h3>
                <div className="text-sm text-gray-500">
                  {wordCount} 字符
                </div>
              </div>
              
              <textarea
                ref={textareaRef}
                {...register('medicalRecord', { 
                  required: '请输入病历内容',
                  minLength: { value: 50, message: '病历内容至少需要50个字符' }
                })}
                placeholder="请粘贴或输入完整的病历内容，包括主诉、现病史、体格检查、辅助检查、诊断和治疗方案等..."
                className="textarea h-96 w-full custom-scrollbar"
              />
              
              {errors.medicalRecord && (
                <p className="mt-2 text-sm text-danger-600 flex items-center">
                  <AlertCircle className="h-4 w-4 mr-1" />
                  {errors.medicalRecord.message}
                </p>
              )}
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={isEvaluating}
              className="btn-primary w-full h-12 text-base"
            >
              {isEvaluating ? (
                <>
                  <Loader2 className="h-5 w-5 animate-spin" />
                  正在评估中...
                </>
              ) : (
                <>
                  <Send className="h-5 w-5" />
                  开始评估
                </>
              )}
            </button>
          </form>
        </div>

        {/* Results Section */}
        <div className="results-section-container">
          {evaluationResult ? (
            <EvaluationResult result={evaluationResult} />
          ) : (
            <div className="card p-12 h-full flex items-center justify-center">
              <div className="text-center text-gray-500">
                <FileText className="h-16 w-16 mx-auto mb-4 text-gray-300" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">等待评估</h3>
                <p className="text-gray-600">
                  请在左侧输入病历内容，然后点击"开始评估"按钮
                </p>
                <div className="mt-6 space-y-2 text-sm text-gray-500">
                  <p>• 支持全面的病历质量评估</p>
                  <p>• 提供详细的改进建议</p>
                  <p>• 自动保存评估历史</p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default Evaluator
