import { describe, it, expect, beforeEach, vi } from 'vitest'
import { evaluateMedicalRecord, testLLMConnection } from '../services/llmService'

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
global.localStorage = localStorageMock

// Mock axios
vi.mock('axios', () => ({
  default: {
    post: vi.fn(),
  },
}))

describe('LLM Service', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    localStorageMock.getItem.mockReturnValue('{}')
  })

  describe('evaluateMedicalRecord', () => {
    it('should return mock evaluation when no API config', async () => {
      const testData = {
        content: '患者男性，45岁，主诉胸痛3小时，诊断急性心肌梗死，给予阿司匹林治疗',
        patientInfo: '张某某，男，45岁',
        evaluationType: 'comprehensive'
      }

      const result = await evaluateMedicalRecord(testData)

      expect(result).toHaveProperty('overallScore')
      expect(result).toHaveProperty('categories')
      expect(result).toHaveProperty('summary')
      expect(result).toHaveProperty('recommendations')
      expect(result).toHaveProperty('excerpts')
      expect(typeof result.overallScore).toBe('number')
      expect(Array.isArray(result.categories)).toBe(true)
      expect(Array.isArray(result.recommendations)).toBe(true)
      expect(Array.isArray(result.excerpts)).toBe(true)
    })

    it('should generate higher scores for comprehensive medical records', async () => {
      const comprehensiveRecord = {
        content: `患者姓名：张某某，性别：男，年龄：45岁
        主诉：胸痛3小时
        现病史：患者3小时前无明显诱因出现胸骨后疼痛，呈压榨性，向左肩背部放射，伴出汗、恶心
        既往史：高血压病史5年
        体格检查：T 36.8℃，P 95次/分，R 20次/分，BP 150/90mmHg
        辅助检查：心电图示V1-V4导联ST段抬高
        诊断：急性前壁心肌梗死
        治疗：阿司匹林、氯吡格雷双联抗血小板治疗`,
        evaluationType: 'comprehensive'
      }

      const simpleRecord = {
        content: '胸痛，心肌梗死',
        evaluationType: 'comprehensive'
      }

      const comprehensiveResult = await evaluateMedicalRecord(comprehensiveRecord)
      const simpleResult = await evaluateMedicalRecord(simpleRecord)

      expect(comprehensiveResult.overallScore).toBeGreaterThan(simpleResult.overallScore)
    })

    it('should handle different evaluation types', async () => {
      const testData = {
        content: '患者男性，45岁，主诉胸痛，诊断心肌梗死',
        evaluationType: 'clinical'
      }

      const result = await evaluateMedicalRecord(testData)
      expect(result).toHaveProperty('overallScore')
    })
  })

  describe('testLLMConnection', () => {
    it('should throw error when no config provided', async () => {
      await expect(testLLMConnection()).rejects.toThrow('请先配置LLM端点和API密钥')
    })

    it('should return success when API responds correctly', async () => {
      localStorageMock.getItem.mockReturnValue(JSON.stringify({
        endpoint: 'https://api.openai.com/v1/chat/completions',
        apiKey: 'test-key',
        modelName: 'gpt-3.5-turbo'
      }))

      const axios = await import('axios')
      axios.default.post.mockResolvedValue({
        data: {
          choices: [{ message: { content: '连接成功' } }]
        }
      })

      const result = await testLLMConnection()
      expect(result.success).toBe(true)
      expect(result.message).toBe('连接成功')
    })
  })
})
