import React, { createContext, useContext, useState, useEffect } from 'react'
import authService from '../services/authService'

const AuthContext = createContext()

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isAuthenticated, setIsAuthenticated] = useState(false)

  useEffect(() => {
    // 检查是否已登录
    const checkAuth = async () => {
      try {
        if (authService.isAuthenticated()) {
          const currentUser = await authService.refreshUser()
          if (currentUser) {
            setUser(currentUser)
            setIsAuthenticated(true)
          } else {
            // Token 无效，清除登录状态
            authService.logout()
            setUser(null)
            setIsAuthenticated(false)
          }
        }
      } catch (error) {
        console.error('Auth check failed:', error)
        authService.logout()
        setUser(null)
        setIsAuthenticated(false)
      } finally {
        setIsLoading(false)
      }
    }

    checkAuth()
  }, [])

  const login = (userData) => {
    console.log('AuthContext login called with:', userData)
    setUser(userData)
    setIsAuthenticated(true)
    console.log('AuthContext state updated - isAuthenticated: true')
  }

  const logout = () => {
    authService.logout()
    setUser(null)
    setIsAuthenticated(false)
  }

  const hasPermission = (permission) => {
    return authService.hasPermission(permission)
  }

  const value = {
    user,
    isAuthenticated,
    isLoading,
    login,
    logout,
    hasPermission
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export default AuthContext
