// Mock data for testing the History page
export const generateMockEvaluations = () => {
  const mockEvaluations = [
    {
      id: 'eval-001-2024-001',
      timestamp: new Date('2024-01-15T10:30:00').toISOString(),
      evaluationType: 'comprehensive',
      content: '患者张某，男，45岁，因"胸痛3小时"入院。患者于今日上午7时无明显诱因出现胸骨后疼痛，呈压榨性，向左肩背部放射，伴恶心、出汗，无发热、咳嗽。既往有高血压病史5年，规律服用降压药物。查体：T 36.8℃，P 88次/分，R 20次/分，BP 150/90mmHg。心界不大，心率88次/分，律齐，各瓣膜听诊区未闻及病理性杂音。双肺呼吸音清，未闻及干湿性啰音。腹部平软，无压痛。',
      patientInfo: '张某，男，45岁，高血压病史',
      result: {
        overallScore: 92,
        categories: [
          { name: '病史采集', score: 95 },
          { name: '体格检查', score: 90 },
          { name: '诊断分析', score: 88 },
          { name: '治疗方案', score: 94 }
        ],
        summary: '该病历记录详细完整，病史采集全面，体格检查规范，诊断思路清晰，治疗方案合理。建议进一步完善心电图和心肌酶检查结果的记录。',
        recommendations: [
          '建议补充心电图检查结果的详细描述',
          '可以增加心肌酶谱的具体数值',
          '治疗方案中可以更详细地说明用药剂量和频次'
        ]
      }
    },
    {
      id: 'eval-002-2024-002',
      timestamp: new Date('2024-01-14T14:20:00').toISOString(),
      evaluationType: 'clinical',
      content: '患者李某，女，32岁，因"发热、咳嗽2天"来诊。患者2天前无明显诱因出现发热，最高体温39.2℃，伴咳嗽、咳痰，痰液为白色粘痰，无胸痛、气促。查体：T 38.5℃，P 96次/分，R 22次/分，BP 120/80mmHg。咽部充血，扁桃体无肿大，颈部淋巴结无肿大。胸部对称，双肺呼吸音粗，右下肺可闻及湿性啰音。',
      patientInfo: '李某，女，32岁',
      result: {
        overallScore: 78,
        categories: [
          { name: '病史采集', score: 82 },
          { name: '体格检查', score: 75 },
          { name: '诊断分析', score: 76 },
          { name: '治疗方案', score: 79 }
        ],
        summary: '病历记录基本完整，但在体格检查和诊断分析方面有待改进。建议补充更详细的肺部体征描述和实验室检查结果。',
        recommendations: [
          '建议补充血常规、C反应蛋白等实验室检查结果',
          '肺部体征描述可以更加详细和准确',
          '可以考虑胸部X线检查以排除肺炎',
          '治疗方案需要更加具体和个体化'
        ]
      }
    },
    {
      id: 'eval-003-2024-003',
      timestamp: new Date('2024-01-13T09:15:00').toISOString(),
      evaluationType: 'documentation',
      content: '患者王某，男，28岁，外伤后右手疼痛1小时。患者1小时前工作时不慎被机器夹伤右手，当时疼痛剧烈，有少量出血。查体：右手中指末节肿胀，皮肤破损约1cm，无活动性出血，指甲完整。X线示右手中指末节骨折。',
      patientInfo: '王某，男，28岁，外伤',
      result: {
        overallScore: 65,
        categories: [
          { name: '病史采集', score: 70 },
          { name: '体格检查', score: 68 },
          { name: '诊断分析', score: 60 },
          { name: '治疗方案', score: 62 }
        ],
        summary: '病历记录过于简单，缺乏必要的详细信息。需要补充更多的病史、体格检查细节和治疗计划。',
        recommendations: [
          '需要详细记录外伤机制和受伤过程',
          '体格检查应包括神经血管功能评估',
          '应记录疼痛评分和功能障碍程度',
          '治疗方案需要更加完整和规范',
          '应包括随访计划和注意事项'
        ]
      }
    }
  ]

  // Store in localStorage for testing
  localStorage.setItem('evaluationHistory', JSON.stringify(mockEvaluations))
  return mockEvaluations
}

// Function to clear mock data
export const clearMockData = () => {
  localStorage.removeItem('evaluationHistory')
}
