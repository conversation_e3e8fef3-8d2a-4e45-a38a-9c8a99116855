# 认证功能实现文档

## 🔧 1. 设置页面模型选择优化

### 修改内容
- **原来**：下拉选择预设模型
- **现在**：自定义输入模型名称 + 快速选择常用模型

### 新功能特点
- ✅ **自定义输入**：支持输入任意模型名称
- ✅ **快速选择**：保留常用模型的快速选择功能
- ✅ **表单验证**：必填字段验证和错误提示
- ✅ **用户友好**：提供输入提示和说明文字

### 使用方式
1. 可以直接在输入框中输入模型名称
2. 也可以点击上方下拉菜单快速选择常用模型
3. 支持的模型格式：`gpt-4`, `claude-3-opus`, `gemini-pro` 等

---

## 🔐 2. 登录认证系统

### 核心组件

#### AuthService (`src/services/authService.js`)
- 用户认证服务
- 本地存储管理
- 权限控制
- Token 管理

#### AuthContext (`src/contexts/AuthContext.jsx`)
- React Context 状态管理
- 全局认证状态
- 用户信息共享

#### ProtectedRoute (`src/components/ProtectedRoute.jsx`)
- 路由保护组件
- 权限验证
- 自动重定向

### 演示账号

| 用户名 | 密码 | 角色 | 权限 |
|--------|------|------|------|
| `admin` | `admin123` | 系统管理员 | 所有权限 |
| `doctor` | `doctor123` | 医生 | 读写、评估 |
| `nurse` | `nurse123` | 护士 | 读取、评估 |

### 权限系统

#### 权限类型
- `read` - 查看权限
- `write` - 编辑权限  
- `evaluate` - 评估权限
- `delete` - 删除权限
- `manage_users` - 用户管理
- `manage_settings` - 系统设置

#### 角色权限映射
- **管理员**：所有权限
- **医生**：read, write, evaluate
- **护士**：read, evaluate

### 页面功能

#### 登录页面 (`/login`)
- ✅ 用户名/密码登录
- ✅ 密码显示/隐藏切换
- ✅ 演示账号快速填入
- ✅ 表单验证和错误提示
- ✅ 加载状态显示

#### 用户管理页面 (`/users`) - 仅管理员
- ✅ 用户列表展示
- ✅ 角色和权限显示
- ✅ 搜索和筛选功能
- ✅ 用户操作（编辑/删除）

#### 认证演示页面 (`/auth-demo`)
- ✅ 当前用户信息展示
- ✅ 权限列表和状态
- ✅ 角色说明
- ✅ 会话管理

### 界面集成

#### 顶部导航栏
- ✅ 用户头像和信息显示
- ✅ 用户下拉菜单
- ✅ 登出功能
- ✅ 快速设置入口

#### 侧边栏
- ✅ 基于角色的菜单显示
- ✅ 管理员专用菜单项
- ✅ 权限控制

### 安全特性

#### Token 管理
- ✅ JWT 风格的 Token 生成
- ✅ Token 过期验证（24小时）
- ✅ 自动登出过期会话

#### 路由保护
- ✅ 未登录自动重定向到登录页
- ✅ 权限不足显示拒绝页面
- ✅ 登录后重定向到原页面

#### 数据安全
- ✅ 密码不在前端存储
- ✅ 用户信息本地缓存
- ✅ 登出时清理所有数据

---

## 🚀 使用指南

### 首次访问
1. 访问 `http://localhost:3002/`
2. 自动重定向到登录页面
3. 选择演示账号或手动输入
4. 登录成功后进入系统

### 角色体验
- **管理员**：可访问所有功能，包括用户管理
- **医生**：可进行病历评估和查看历史
- **护士**：可查看内容和基础评估

### 权限测试
- 访问 `/auth-demo` 查看当前权限
- 尝试访问 `/users` 测试权限控制
- 不同角色登录体验不同功能

---

## 📁 文件结构

```
src/
├── services/
│   └── authService.js          # 认证服务
├── contexts/
│   └── AuthContext.jsx         # 认证上下文
├── components/
│   ├── ProtectedRoute.jsx      # 路由保护
│   └── Layout.jsx              # 布局组件（已更新）
├── pages/
│   ├── Login.jsx               # 登录页面
│   ├── UserManagement.jsx      # 用户管理
│   ├── AuthDemo.jsx            # 认证演示
│   └── Settings.jsx            # 设置页面（已更新）
└── App.jsx                     # 主应用（已更新）
```

---

## 🎯 下一步建议

1. **测试登录功能**：尝试不同角色登录
2. **验证权限控制**：测试不同角色的页面访问
3. **体验模型设置**：在设置页面测试自定义模型输入
4. **用户管理**：使用管理员账号测试用户管理功能
