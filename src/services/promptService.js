// 提示词管理服务
import { v4 as uuidv4 } from 'uuid'

// 默认提示词模板
const DEFAULT_PROMPTS = [
  {
    id: 'comprehensive',
    name: '综合评估',
    description: '全面评估病历的完整性、准确性、规范性和逻辑性',
    category: '通用',
    isDefault: true,
    content: `你是一位资深的医疗质量专家，请对以下病历进行全面评估。

评估维度：
1. 完整性 (30分) - 病历内容是否完整，包含必要的医疗信息
2. 准确性 (25分) - 诊断和治疗是否准确合理
3. 规范性 (25分) - 病历书写是否符合医疗规范
4. 逻辑性 (20分) - 诊疗过程是否逻辑清晰

请按以下JSON格式返回评估结果：
{
  "overallScore": 85,
  "categories": [
    {"name": "完整性", "score": 90, "description": "病历内容完整度评估"},
    {"name": "准确性", "score": 85, "description": "诊断治疗准确性评估"},
    {"name": "规范性", "score": 80, "description": "书写规范性评估"},
    {"name": "逻辑性", "score": 85, "description": "诊疗逻辑性评估"}
  ],
  "summary": "整体评估总结",
  "recommendations": ["改进建议1", "改进建议2"],
  "excerpts": [
    {"text": "原文摘录", "category": "评估维度", "score": 85, "comment": "评价说明"}
  ]
}`
  },
  {
    id: 'clinical',
    name: '临床质量',
    description: '重点评估临床决策的准确性和合理性',
    category: '通用',
    isDefault: true,
    content: `你是一位临床医学专家，请重点评估以下病历的临床质量。

评估重点：
1. 诊断准确性 (40分) - 诊断是否准确、完整
2. 治疗合理性 (35分) - 治疗方案是否合理、有效
3. 检查必要性 (25分) - 辅助检查是否必要、充分

请按JSON格式返回评估结果，重点关注临床决策的合理性。`
  },
  {
    id: 'documentation',
    name: '文档规范',
    description: '评估病历书写的规范性和标准化程度',
    category: '通用',
    isDefault: true,
    content: `你是一位医疗文档规范专家，请重点评估以下病历的书写规范。

评估重点：
1. 格式规范 (35分) - 病历格式是否标准
2. 用词准确 (30分) - 医学术语使用是否准确
3. 逻辑结构 (35分) - 内容组织是否合理

请按JSON格式返回评估结果，重点关注文档规范性。`
  },
  {
    id: 'dental-expert',
    name: '口腔科评审专家',
    description: '基于口腔科诊断准确性和格式规范性评分表的专业评估',
    category: '专科',
    isDefault: true,
    content: `你是一位资深的口腔科评审专家，请根据口腔科病历评审标准对以下病历进行专业评估。

评估标准参考：

**诊断准确性评分表 (100分)**
1. 主诉 (20分)
   - 准确反映患儿主要症状及部位 (10分)
   - 症状描述完整 (10分)

2. 现病史 (20分)
   - 发病时间、持续时间、诱因、进展、伴随症状、缓解因素完整 
   - 每缺失1要素扣4分

3. 口腔检查 (20分)
   - 牙齿（牙位、龋坏分级）、软组织、咬合关系完整描述
   - 牙位记录FDI标准 (10分)，龋坏分级准确 (10分)

4. 诊断 (30分)
   - 与检查所见一致，ICD-11编码正确
   - 主要诊断 (20分)，次要诊断 (10分)

5. 处置计划 (10分)
   - 治疗方案、医嘱、复诊计划完整
   - 方案完整 (5分)，医嘱明确 (5分)

**格式规范性评分表 (100分)**
1. 基本信息 (15分) - 姓名、性别、年龄、病历号、日期
2. 主诉 (10分) - 简洁≤20字，症状+时间
3. 现病史 (15分) - 时间轴清晰，无医学术语错误
4. 检查 (25分) - 牙位、龋坏分级、软组织描述完整
5. 诊断 (20分) - 使用ICD-11标准术语
6. 处置 (15分) - 治疗方案、知情同意、复诊时间

请按以下JSON格式返回评估结果：
{
  "overallScore": 85,
  "categories": [
    {"name": "主诉", "score": 90, "description": "主诉内容准确性和完整性"},
    {"name": "现病史", "score": 85, "description": "病史记录的完整性和逻辑性"},
    {"name": "口腔检查", "score": 80, "description": "检查记录的专业性和准确性"},
    {"name": "诊断", "score": 85, "description": "诊断的准确性和规范性"},
    {"name": "处置计划", "score": 80, "description": "治疗方案的合理性和完整性"},
    {"name": "格式规范", "score": 85, "description": "病历格式和术语规范性"}
  ],
  "summary": "基于口腔科专业标准的综合评估",
  "recommendations": ["具体改进建议"],
  "excerpts": [
    {"text": "原文摘录", "category": "评估维度", "score": 85, "comment": "专业评价"}
  ]
}`
  }
]

// 获取所有提示词
export const getAllPrompts = () => {
  const savedPrompts = JSON.parse(localStorage.getItem('customPrompts') || '[]')
  return [...DEFAULT_PROMPTS, ...savedPrompts]
}

// 获取单个提示词
export const getPromptById = (id) => {
  const allPrompts = getAllPrompts()
  return allPrompts.find(prompt => prompt.id === id)
}

// 保存提示词
export const savePrompt = (promptData) => {
  const savedPrompts = JSON.parse(localStorage.getItem('customPrompts') || '[]')
  
  if (promptData.id) {
    // 更新现有提示词
    const index = savedPrompts.findIndex(p => p.id === promptData.id)
    if (index !== -1) {
      savedPrompts[index] = { ...promptData, updatedAt: new Date().toISOString() }
    }
  } else {
    // 创建新提示词
    const newPrompt = {
      ...promptData,
      id: uuidv4(),
      isDefault: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    savedPrompts.push(newPrompt)
  }
  
  localStorage.setItem('customPrompts', JSON.stringify(savedPrompts))
  return savedPrompts
}

// 删除提示词
export const deletePrompt = (id) => {
  const savedPrompts = JSON.parse(localStorage.getItem('customPrompts') || '[]')
  const filteredPrompts = savedPrompts.filter(p => p.id !== id)
  localStorage.setItem('customPrompts', JSON.stringify(filteredPrompts))
  return filteredPrompts
}

// 导出提示词配置
export const exportPrompts = () => {
  const allPrompts = getAllPrompts()
  const exportData = {
    version: '1.0',
    exportDate: new Date().toISOString(),
    prompts: allPrompts.filter(p => !p.isDefault) // 只导出自定义提示词
  }
  
  const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `prompts-${new Date().toISOString().split('T')[0]}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

// 导入提示词配置
export const importPrompts = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const importData = JSON.parse(e.target.result)
        if (!importData.prompts || !Array.isArray(importData.prompts)) {
          throw new Error('无效的提示词配置文件')
        }
        
        const savedPrompts = JSON.parse(localStorage.getItem('customPrompts') || '[]')
        const newPrompts = importData.prompts.map(prompt => ({
          ...prompt,
          id: uuidv4(), // 重新生成ID避免冲突
          isDefault: false,
          importedAt: new Date().toISOString()
        }))
        
        const updatedPrompts = [...savedPrompts, ...newPrompts]
        localStorage.setItem('customPrompts', JSON.stringify(updatedPrompts))
        resolve(newPrompts.length)
      } catch (error) {
        reject(error)
      }
    }
    reader.onerror = () => reject(new Error('文件读取失败'))
    reader.readAsText(file)
  })
}

// 获取提示词分类
export const getPromptCategories = () => {
  const allPrompts = getAllPrompts()
  const categories = [...new Set(allPrompts.map(p => p.category))]
  return categories.map(category => ({
    name: category,
    count: allPrompts.filter(p => p.category === category).length
  }))
}
