// 认证服务
class AuthService {
  constructor() {
    this.TOKEN_KEY = 'medical_record_token'
    this.USER_KEY = 'medical_record_user'
  }

  // 模拟用户数据库
  users = [
    {
      id: 1,
      username: 'admin',
      password: 'admin123',
      name: '系统管理员',
      role: 'admin',
      department: '信息科',
      avatar: null
    },
    {
      id: 2,
      username: 'doctor',
      password: 'doctor123',
      name: '张医生',
      role: 'doctor',
      department: '内科',
      avatar: null
    },
    {
      id: 3,
      username: 'nurse',
      password: 'nurse123',
      name: '李护士',
      role: 'nurse',
      department: '内科',
      avatar: null
    }
  ]

  // 登录
  async login(username, password) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const user = this.users.find(u => u.username === username && u.password === password)
        
        if (user) {
          const token = this.generateToken(user)
          const userInfo = { ...user }
          delete userInfo.password // 不返回密码
          
          // 保存到本地存储
          localStorage.setItem(this.TOKEN_KEY, token)
          localStorage.setItem(this.USER_KEY, JSON.stringify(userInfo))
          
          resolve({
            success: true,
            token,
            user: userInfo,
            message: '登录成功'
          })
        } else {
          reject({
            success: false,
            message: '用户名或密码错误'
          })
        }
      }, 1000) // 模拟网络延迟
    })
  }

  // 登出
  logout() {
    localStorage.removeItem(this.TOKEN_KEY)
    localStorage.removeItem(this.USER_KEY)
  }

  // 检查是否已登录
  isAuthenticated() {
    const token = localStorage.getItem(this.TOKEN_KEY)
    const user = localStorage.getItem(this.USER_KEY)
    return !!(token && user)
  }

  // 获取当前用户信息
  getCurrentUser() {
    const userStr = localStorage.getItem(this.USER_KEY)
    return userStr ? JSON.parse(userStr) : null
  }

  // 获取token
  getToken() {
    return localStorage.getItem(this.TOKEN_KEY)
  }

  // 生成token（简单实现）
  generateToken(user) {
    const timestamp = Date.now()
    const payload = {
      userId: user.id,
      username: user.username,
      role: user.role,
      timestamp
    }
    return btoa(JSON.stringify(payload))
  }

  // 验证token
  validateToken(token) {
    try {
      const payload = JSON.parse(atob(token))
      const now = Date.now()
      const tokenAge = now - payload.timestamp
      const maxAge = 24 * 60 * 60 * 1000 // 24小时

      return tokenAge < maxAge
    } catch (error) {
      return false
    }
  }

  // 刷新用户信息
  async refreshUser() {
    const token = this.getToken()
    if (!token || !this.validateToken(token)) {
      this.logout()
      return null
    }

    const user = this.getCurrentUser()
    if (user) {
      // 这里可以从服务器重新获取用户信息
      return user
    }
    return null
  }

  // 修改密码
  async changePassword(oldPassword, newPassword) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const user = this.getCurrentUser()
        if (!user) {
          reject({ success: false, message: '用户未登录' })
          return
        }

        const dbUser = this.users.find(u => u.id === user.id)
        if (dbUser && dbUser.password === oldPassword) {
          dbUser.password = newPassword
          resolve({ success: true, message: '密码修改成功' })
        } else {
          reject({ success: false, message: '原密码错误' })
        }
      }, 500)
    })
  }

  // 获取用户权限
  getUserPermissions(role) {
    const permissions = {
      admin: ['read', 'write', 'delete', 'manage_users', 'manage_settings'],
      doctor: ['read', 'write', 'evaluate'],
      nurse: ['read', 'evaluate']
    }
    return permissions[role] || ['read']
  }

  // 检查权限
  hasPermission(permission) {
    const user = this.getCurrentUser()
    if (!user) return false
    
    const permissions = this.getUserPermissions(user.role)
    return permissions.includes(permission)
  }
}

// 创建单例实例
const authService = new AuthService()
export default authService
