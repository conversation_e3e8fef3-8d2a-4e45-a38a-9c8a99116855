import React, { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import toast from 'react-hot-toast'
import {
  Plus,
  Edit3,
  Trash2,
  Copy,
  Download,
  Upload,
  Eye,
  EyeOff,
  Save,
  X,
  FileText,
  Tag,
  Calendar,
  AlertCircle
} from 'lucide-react'
import {
  getAllPrompts,
  savePrompt,
  deletePrompt,
  exportPrompts,
  importPrompts,
  getPromptCategories
} from '../services/promptService'

const PromptManager = () => {
  const [prompts, setPrompts] = useState([])
  const [categories, setCategories] = useState([])
  const [selectedCategory, setSelectedCategory] = useState('全部')
  const [isEditing, setIsEditing] = useState(false)
  const [editingPrompt, setEditingPrompt] = useState(null)
  const [previewPrompt, setPreviewPrompt] = useState(null)
  const [searchTerm, setSearchTerm] = useState('')

  const { register, handleSubmit, reset, watch, formState: { errors } } = useForm({
    defaultValues: {
      name: '',
      description: '',
      category: '通用',
      content: ''
    }
  })

  useEffect(() => {
    loadPrompts()
  }, [])

  const loadPrompts = () => {
    const allPrompts = getAllPrompts()
    const promptCategories = getPromptCategories()
    setPrompts(allPrompts)
    setCategories([{ name: '全部', count: allPrompts.length }, ...promptCategories])
  }

  const filteredPrompts = prompts.filter(prompt => {
    const matchesCategory = selectedCategory === '全部' || prompt.category === selectedCategory
    const matchesSearch = prompt.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         prompt.description.toLowerCase().includes(searchTerm.toLowerCase())
    return matchesCategory && matchesSearch
  })

  const handleCreateNew = () => {
    setEditingPrompt(null)
    setIsEditing(true)
    reset({
      name: '',
      description: '',
      category: '通用',
      content: ''
    })
  }

  const handleEdit = (prompt) => {
    setEditingPrompt(prompt)
    setIsEditing(true)
    reset({
      name: prompt.name,
      description: prompt.description,
      category: prompt.category,
      content: prompt.content
    })
  }

  const handleDelete = async (prompt) => {
    if (prompt.isDefault) {
      toast.error('默认提示词不能删除')
      return
    }

    if (window.confirm(`确定要删除提示词"${prompt.name}"吗？此操作不可撤销。`)) {
      try {
        deletePrompt(prompt.id)
        loadPrompts()
        toast.success('提示词已删除')
      } catch (error) {
        toast.error('删除失败')
      }
    }
  }

  const handleDuplicate = (prompt) => {
    setEditingPrompt(null)
    setIsEditing(true)
    reset({
      name: `${prompt.name} (副本)`,
      description: prompt.description,
      category: prompt.category,
      content: prompt.content
    })
  }

  const onSubmit = async (data) => {
    try {
      const promptData = {
        ...data,
        id: editingPrompt?.id
      }
      savePrompt(promptData)
      loadPrompts()
      setIsEditing(false)
      setEditingPrompt(null)
      toast.success(editingPrompt ? '提示词已更新' : '提示词已创建')
    } catch (error) {
      toast.error('保存失败')
    }
  }

  const handleExport = () => {
    try {
      exportPrompts()
      toast.success('提示词配置已导出')
    } catch (error) {
      toast.error('导出失败')
    }
  }

  const handleImport = async (event) => {
    const file = event.target.files[0]
    if (!file) return

    try {
      const importedCount = await importPrompts(file)
      loadPrompts()
      toast.success(`成功导入 ${importedCount} 个提示词`)
    } catch (error) {
      toast.error(`导入失败: ${error.message}`)
    }
    
    // 清空文件输入
    event.target.value = ''
  }

  const handleCancel = () => {
    setIsEditing(false)
    setEditingPrompt(null)
    reset()
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">提示词管理</h3>
          <p className="text-sm text-gray-600 mt-1">
            创建和管理自定义评估提示词，支持导入导出配置
          </p>
        </div>
        <div className="flex space-x-2">
          <input
            type="file"
            accept=".json"
            onChange={handleImport}
            className="hidden"
            id="import-prompts"
          />
          <label
            htmlFor="import-prompts"
            className="btn-outline cursor-pointer"
          >
            <Upload className="h-4 w-4 mr-2" />
            导入
          </label>
          <button
            onClick={handleExport}
            className="btn-outline"
          >
            <Download className="h-4 w-4 mr-2" />
            导出
          </button>
          <button
            onClick={handleCreateNew}
            className="btn-primary"
          >
            <Plus className="h-4 w-4 mr-2" />
            新建提示词
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <input
            type="text"
            placeholder="搜索提示词..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="input"
          />
        </div>
        <div className="flex space-x-2">
          {categories.map(category => (
            <button
              key={category.name}
              onClick={() => setSelectedCategory(category.name)}
              className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                selectedCategory === category.name
                  ? 'bg-primary-100 text-primary-700 border border-primary-200'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              {category.name}
              <span className="ml-1 text-xs opacity-75">({category.count})</span>
            </button>
          ))}
        </div>
      </div>

      {/* Prompt List */}
      <div className="grid gap-4">
        {filteredPrompts.map(prompt => (
          <div key={prompt.id} className="card p-4 hover:shadow-md transition-shadow">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-2">
                  <h4 className="font-medium text-gray-900">{prompt.name}</h4>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    prompt.isDefault 
                      ? 'bg-blue-100 text-blue-700' 
                      : 'bg-green-100 text-green-700'
                  }`}>
                    {prompt.isDefault ? '默认' : '自定义'}
                  </span>
                  <span className="px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
                    <Tag className="h-3 w-3 mr-1 inline" />
                    {prompt.category}
                  </span>
                </div>
                <p className="text-sm text-gray-600 mb-2">{prompt.description}</p>
                {(prompt.createdAt || prompt.updatedAt) && (
                  <div className="flex items-center text-xs text-gray-500">
                    <Calendar className="h-3 w-3 mr-1" />
                    {prompt.updatedAt ? `更新于 ${new Date(prompt.updatedAt).toLocaleDateString()}` : 
                     prompt.createdAt ? `创建于 ${new Date(prompt.createdAt).toLocaleDateString()}` : ''}
                  </div>
                )}
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setPreviewPrompt(prompt)}
                  className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                  title="预览"
                >
                  <Eye className="h-4 w-4" />
                </button>
                <button
                  onClick={() => handleDuplicate(prompt)}
                  className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                  title="复制"
                >
                  <Copy className="h-4 w-4" />
                </button>
                <button
                  onClick={() => handleEdit(prompt)}
                  className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                  title="编辑"
                >
                  <Edit3 className="h-4 w-4" />
                </button>
                {!prompt.isDefault && (
                  <button
                    onClick={() => handleDelete(prompt)}
                    className="p-2 text-gray-400 hover:text-red-600 rounded-lg hover:bg-red-50"
                    title="删除"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {filteredPrompts.length === 0 && (
        <div className="text-center py-12">
          <FileText className="h-12 w-12 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">暂无提示词</h3>
          <p className="text-gray-600 mb-4">
            {searchTerm || selectedCategory !== '全部' 
              ? '没有找到匹配的提示词，请尝试调整搜索条件' 
              : '还没有创建任何提示词，点击上方按钮开始创建'}
          </p>
        </div>
      )}

      {/* Edit Modal */}
      {isEditing && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
            <div className="flex items-center justify-between p-6 border-b">
              <h3 className="text-lg font-semibold text-gray-900">
                {editingPrompt ? '编辑提示词' : '新建提示词'}
              </h3>
              <button
                onClick={handleCancel}
                className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            
            <form onSubmit={handleSubmit(onSubmit)} className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      名称 <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      {...register('name', { required: '请输入提示词名称' })}
                      className="input"
                      placeholder="输入提示词名称"
                    />
                    {errors.name && (
                      <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
                    )}
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      分类
                    </label>
                    <select {...register('category')} className="input">
                      <option value="通用">通用</option>
                      <option value="专科">专科</option>
                      <option value="质控">质控</option>
                      <option value="教学">教学</option>
                      <option value="其他">其他</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    描述
                  </label>
                  <input
                    type="text"
                    {...register('description')}
                    className="input"
                    placeholder="简要描述提示词的用途和特点"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    提示词内容 <span className="text-red-500">*</span>
                  </label>
                  <textarea
                    {...register('content', { required: '请输入提示词内容' })}
                    rows={12}
                    className="input resize-none"
                    placeholder="输入详细的提示词内容..."
                  />
                  {errors.content && (
                    <p className="mt-1 text-sm text-red-600">{errors.content.message}</p>
                  )}
                  <p className="mt-1 text-xs text-gray-500">
                    提示词应包含评估标准、评分维度和返回格式要求
                  </p>
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6 pt-6 border-t">
                <button
                  type="button"
                  onClick={handleCancel}
                  className="btn-secondary"
                >
                  取消
                </button>
                <button
                  type="submit"
                  className="btn-primary"
                >
                  <Save className="h-4 w-4 mr-2" />
                  {editingPrompt ? '更新' : '创建'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Preview Modal */}
      {previewPrompt && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
            <div className="flex items-center justify-between p-6 border-b">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">{previewPrompt.name}</h3>
                <p className="text-sm text-gray-600 mt-1">{previewPrompt.description}</p>
              </div>
              <button
                onClick={() => setPreviewPrompt(null)}
                className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
              <div className="bg-gray-50 rounded-lg p-4">
                <pre className="whitespace-pre-wrap text-sm text-gray-800 font-mono">
                  {previewPrompt.content}
                </pre>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default PromptManager
