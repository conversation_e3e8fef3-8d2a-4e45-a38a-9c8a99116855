import React, { useState } from 'react'
import {
  CheckCircle,
  AlertTriangle,
  XCircle,
  TrendingUp,
  FileText,
  Copy,
  Download,
  Eye,
  EyeOff,
  BarChart3,
  Book<PERSON>pen,
  Maximize2,
  Minimize2
} from 'lucide-react'
import toast from 'react-hot-toast'

const EvaluationResult = ({ result, originalContent, patientInfo }) => {
  const [showDetails, setShowDetails] = useState(true)
  const [showOriginalContent, setShowOriginalContent] = useState(false)
  const [copiedSection, setCopiedSection] = useState(null)

  if (!result) return null

  const getScoreColor = (score) => {
    if (score >= 90) return 'text-success-600 bg-success-100'
    if (score >= 80) return 'text-primary-600 bg-primary-100'
    if (score >= 70) return 'text-warning-600 bg-warning-100'
    return 'text-danger-600 bg-danger-100'
  }

  const getScoreIcon = (score) => {
    if (score >= 80) return CheckCircle
    if (score >= 60) return AlertTriangle
    return XCircle
  }

  const handleCopy = async (text, section) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedSection(section)
      toast.success(`已复制${section}内容`)
      setTimeout(() => setCopiedSection(null), 2000)
    } catch (error) {
      toast.error('复制失败')
    }
  }

  const handleExport = () => {
    const exportData = {
      timestamp: new Date().toISOString(),
      patientInfo: patientInfo,
      originalContent: originalContent,
      overallScore: result.overallScore,
      categories: result.categories,
      summary: result.summary,
      recommendations: result.recommendations,
      excerpts: result.excerpts
    }
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `medical-evaluation-${Date.now()}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    toast.success('评估结果已导出')
  }

  return (
    <div className="evaluation-result-container animate-fade-in">
      {/* Fixed Header with Overall Score */}
      <div className="evaluation-result-header">
        <div className="card p-6 mb-6 animate-slide-up">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">评估结果</h3>
            <div className="flex space-x-2">
              <button
                onClick={() => setShowDetails(!showDetails)}
                className="btn-outline text-sm"
              >
                {showDetails ? <EyeOff className="h-4 w-4 mr-1" /> : <Eye className="h-4 w-4 mr-1" />}
                {showDetails ? '隐藏详情' : '显示详情'}
              </button>
              <button
                onClick={handleExport}
                className="btn-outline text-sm"
              >
                <Download className="h-4 w-4 mr-1" />
                导出
              </button>
            </div>
          </div>

          <div className="text-center">
            <div className={`inline-flex items-center justify-center w-20 h-20 rounded-full text-2xl font-bold animate-scale-in ${getScoreColor(result.overallScore)}`}>
              {result.overallScore}
            </div>
            <p className="mt-2 text-lg font-semibold text-gray-900">总体评分</p>
            <p className="text-gray-600">
              {result.overallScore >= 90 ? '优秀' :
               result.overallScore >= 80 ? '良好' :
               result.overallScore >= 70 ? '一般' : '需要改进'}
            </p>
          </div>
        </div>
      </div>

      {/* Scrollable Content Area */}
      <div className="evaluation-result-content">
        <div className="space-y-6">

          {/* Original Medical Record Content */}
          {originalContent && (
            <div className="card p-6">
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-lg font-semibold text-gray-900 flex items-center">
                  <BookOpen className="h-5 w-5 mr-2 text-indigo-600" />
                  原始病历内容
                  <span className="ml-2 px-2 py-1 text-xs bg-indigo-100 text-indigo-700 rounded-full">
                    {originalContent.length} 字符
                  </span>
                </h4>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handleCopy(originalContent, '病历内容')}
                    className="btn-outline text-sm"
                  >
                    <Copy className="h-4 w-4 mr-1" />
                    {copiedSection === '病历内容' ? '已复制' : '复制'}
                  </button>
                  <button
                    onClick={() => setShowOriginalContent(!showOriginalContent)}
                    className="btn-outline text-sm"
                  >
                    {showOriginalContent ? <Minimize2 className="h-4 w-4 mr-1" /> : <Maximize2 className="h-4 w-4 mr-1" />}
                    {showOriginalContent ? '收起' : '展开'}
                  </button>
                </div>
              </div>

              <div className={`bg-gradient-to-r from-indigo-50 to-blue-50 rounded-lg border border-indigo-200 transition-all duration-300 ${
                showOriginalContent ? 'p-4' : 'p-4'
              }`}>
                <div className={`bg-white rounded-lg border border-gray-200 p-4 transition-all duration-300 ${
                  showOriginalContent ? 'medical-record-expanded' : 'medical-record-collapsed'
                }`}>
                  <div className="prose prose-sm max-w-none">
                    <div className="medical-record-content text-gray-700 text-sm">
                      {originalContent}
                    </div>
                  </div>
                  {!showOriginalContent && originalContent.length > 300 && (
                    <div className="medical-record-fade-overlay"></div>
                  )}
                </div>
                {!showOriginalContent && originalContent.length > 300 && (
                  <div className="text-center mt-2">
                    <button
                      onClick={() => setShowOriginalContent(true)}
                      className="text-sm text-indigo-600 hover:text-indigo-800 font-medium"
                    >
                      点击展开查看完整内容
                    </button>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Patient Information */}
          {patientInfo && (
            <div className="card p-6">
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-lg font-semibold text-gray-900 flex items-center">
                  <FileText className="h-5 w-5 mr-2 text-blue-600" />
                  患者信息
                </h4>
                <button
                  onClick={() => handleCopy(patientInfo, '患者信息')}
                  className="btn-outline text-sm"
                >
                  <Copy className="h-4 w-4 mr-1" />
                  {copiedSection === '患者信息' ? '已复制' : '复制'}
                </button>
              </div>
              <div className="bg-gradient-to-r from-blue-50 to-cyan-50 rounded-lg p-4 border border-blue-200">
                <div className="prose prose-sm max-w-none">
                  <p className="text-gray-700 leading-relaxed">{patientInfo}</p>
                </div>
              </div>
            </div>
          )}

          {/* Category Scores */}
          {showDetails && result.categories && (
            <div className="card p-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <BarChart3 className="h-5 w-5 mr-2 text-blue-600" />
                分项评分
              </h4>
              <div className="space-y-3">
                {result.categories.map((category, index) => {
                  const Icon = getScoreIcon(category.score)
                  return (
                    <div key={index} className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg border border-gray-200 hover:shadow-sm transition-shadow">
                      <div className="flex items-center flex-1 min-w-0">
                        <Icon className={`h-5 w-5 mr-3 flex-shrink-0 ${
                          category.score >= 80 ? 'text-success-500' :
                          category.score >= 60 ? 'text-warning-500' : 'text-danger-500'
                        }`} />
                        <div className="min-w-0 flex-1">
                          <p className="font-medium text-gray-900 truncate">{category.name}</p>
                          <p className="text-sm text-gray-600 truncate">{category.description}</p>
                        </div>
                      </div>
                      <div className="text-right ml-4 flex-shrink-0">
                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getScoreColor(category.score)}`}>
                          {category.score}分
                        </span>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>
          )}

          {/* Summary */}
          {showDetails && result.summary && (
            <div className="card p-6">
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-lg font-semibold text-gray-900 flex items-center">
                  <FileText className="h-5 w-5 mr-2 text-green-600" />
                  评估总结
                </h4>
                <button
                  onClick={() => handleCopy(result.summary, '评估总结')}
                  className="btn-outline text-sm"
                >
                  <Copy className="h-4 w-4 mr-1" />
                  {copiedSection === '评估总结' ? '已复制' : '复制'}
                </button>
              </div>
              <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-4 border border-green-200">
                <div className="prose prose-sm max-w-none">
                  <p className="text-gray-700 leading-relaxed text-base">{result.summary}</p>
                </div>
              </div>
            </div>
          )}

          {/* Recommendations */}
          {showDetails && result.recommendations && result.recommendations.length > 0 && (
            <div className="card p-6">
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-lg font-semibold text-gray-900 flex items-center">
                  <TrendingUp className="h-5 w-5 mr-2 text-orange-600" />
                  改进建议
                  <span className="ml-2 px-2 py-1 text-xs bg-orange-100 text-orange-700 rounded-full">
                    {result.recommendations.length}项
                  </span>
                </h4>
                <button
                  onClick={() => handleCopy(result.recommendations.join('\n'), '改进建议')}
                  className="btn-outline text-sm"
                >
                  <Copy className="h-4 w-4 mr-1" />
                  {copiedSection === '改进建议' ? '已复制' : '复制'}
                </button>
              </div>
              <div className="space-y-3">
                {result.recommendations.map((recommendation, index) => (
                  <div key={index} className="flex items-start p-4 bg-gradient-to-r from-orange-50 to-yellow-50 rounded-lg border border-orange-200 hover:shadow-sm transition-shadow">
                    <div className="flex-shrink-0 w-6 h-6 bg-orange-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                      <span className="text-xs font-semibold text-orange-700">{index + 1}</span>
                    </div>
                    <p className="text-gray-700 text-sm leading-relaxed flex-1">{recommendation}</p>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Supporting Excerpts */}
          {showDetails && result.excerpts && result.excerpts.length > 0 && (
            <div className="card p-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Eye className="h-5 w-5 mr-2 text-purple-600" />
                支持性摘录
                <span className="ml-2 px-2 py-1 text-xs bg-purple-100 text-purple-700 rounded-full">
                  {result.excerpts.length}条
                </span>
              </h4>
              <div className="space-y-4">
                {result.excerpts.map((excerpt, index) => (
                  <div key={index} className="border-l-4 border-purple-400 bg-gradient-to-r from-purple-50 to-indigo-50 rounded-r-lg p-4 hover:shadow-sm transition-shadow">
                    <div className="flex items-center justify-between mb-3">
                      <span className="text-sm font-medium text-purple-700 bg-purple-100 px-2 py-1 rounded-full">
                        {excerpt.category || `摘录 ${index + 1}`}
                      </span>
                      <span className={`text-xs px-2 py-1 rounded-full font-medium ${getScoreColor(excerpt.score || 0)}`}>
                        {excerpt.score || 0}分
                      </span>
                    </div>
                    <blockquote className="text-gray-700 text-sm bg-white p-3 rounded-lg border border-gray-200 shadow-sm">
                      <span className="text-gray-400 text-lg leading-none">"</span>
                      <span className="italic">{excerpt.text}</span>
                      <span className="text-gray-400 text-lg leading-none">"</span>
                    </blockquote>
                    {excerpt.comment && (
                      <div className="mt-3 p-2 bg-white rounded border border-gray-200">
                        <p className="text-xs text-gray-600 font-medium">评价：{excerpt.comment}</p>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-center space-x-4 pt-6">
            <button
              onClick={() => window.location.reload()}
              className="btn-outline"
            >
              <FileText className="h-4 w-4 mr-2" />
              评估新病历
            </button>
            <button
              onClick={() => window.history.back()}
              className="btn-secondary"
            >
              返回上一页
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default EvaluationResult
