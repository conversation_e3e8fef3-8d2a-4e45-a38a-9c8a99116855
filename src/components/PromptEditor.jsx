import React, { useState, useRef, useEffect, useCallback } from 'react'
import {
  Maximize2,
  Minimize2,
  RotateCcw,
  RotateCw,
  Save,
  Copy,
  Eye,
  EyeOff,
  Type,
  Hash,
  Clock,
  Zap,
  Settings,
  ChevronUp,
  ChevronDown
} from 'lucide-react'
import toast from 'react-hot-toast'

const PromptEditor = ({ 
  value = '', 
  onChange, 
  onSave,
  placeholder = '输入提示词内容...',
  className = '',
  minHeight = 400,
  maxHeight = 800,
  showToolbar = true,
  autoSave = true,
  autoSaveDelay = 2000
}) => {
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [showPreview, setShowPreview] = useState(false)
  const [showStats, setShowStats] = useState(true)
  const [editorHeight, setEditorHeight] = useState(minHeight)
  const [history, setHistory] = useState([value])
  const [historyIndex, setHistoryIndex] = useState(0)
  const [lastSaved, setLastSaved] = useState(null)
  const [isDirty, setIsDirty] = useState(false)
  
  const textareaRef = useRef(null)
  const autoSaveTimeoutRef = useRef(null)
  const resizeHandleRef = useRef(null)

  // 计算文本统计信息
  const stats = {
    characters: value.length,
    charactersNoSpaces: value.replace(/\s/g, '').length,
    words: value.trim() ? value.trim().split(/\s+/).length : 0,
    lines: value.split('\n').length,
    paragraphs: value.split(/\n\s*\n/).filter(p => p.trim()).length
  }

  // 处理内容变化
  const handleChange = useCallback((newValue) => {
    onChange?.(newValue)
    setIsDirty(true)
    
    // 更新历史记录
    if (newValue !== history[historyIndex]) {
      const newHistory = history.slice(0, historyIndex + 1)
      newHistory.push(newValue)
      setHistory(newHistory)
      setHistoryIndex(newHistory.length - 1)
    }

    // 自动保存
    if (autoSave && onSave) {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current)
      }
      autoSaveTimeoutRef.current = setTimeout(() => {
        onSave(newValue)
        setLastSaved(new Date())
        setIsDirty(false)
      }, autoSaveDelay)
    }
  }, [onChange, onSave, autoSave, autoSaveDelay, history, historyIndex])

  // 撤销操作
  const handleUndo = useCallback(() => {
    if (historyIndex > 0) {
      const newIndex = historyIndex - 1
      setHistoryIndex(newIndex)
      const newValue = history[newIndex]
      onChange?.(newValue)
      setIsDirty(true)
    }
  }, [historyIndex, history, onChange])

  // 重做操作
  const handleRedo = useCallback(() => {
    if (historyIndex < history.length - 1) {
      const newIndex = historyIndex + 1
      setHistoryIndex(newIndex)
      const newValue = history[newIndex]
      onChange?.(newValue)
      setIsDirty(true)
    }
  }, [historyIndex, history, onChange])

  // 手动保存
  const handleSave = useCallback(() => {
    if (onSave) {
      onSave(value)
      setLastSaved(new Date())
      setIsDirty(false)
      toast.success('提示词已保存')
    }
  }, [onSave, value])

  // 复制内容
  const handleCopy = useCallback(async () => {
    try {
      await navigator.clipboard.writeText(value)
      toast.success('内容已复制到剪贴板')
    } catch (error) {
      toast.error('复制失败')
    }
  }, [value])

  // 快捷键处理
  const handleKeyDown = useCallback((e) => {
    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case 's':
          e.preventDefault()
          handleSave()
          break
        case 'z':
          if (e.shiftKey) {
            e.preventDefault()
            handleRedo()
          } else {
            e.preventDefault()
            handleUndo()
          }
          break
        case 'y':
          e.preventDefault()
          handleRedo()
          break
        case 'Enter':
          if (e.shiftKey) {
            e.preventDefault()
            setIsFullscreen(!isFullscreen)
          }
          break
      }
    }
    
    if (e.key === 'F11') {
      e.preventDefault()
      setIsFullscreen(!isFullscreen)
    }
  }, [handleSave, handleUndo, handleRedo, isFullscreen])

  // 拖拽调整高度
  const handleMouseDown = useCallback((e) => {
    e.preventDefault()
    const startY = e.clientY
    const startHeight = editorHeight

    const handleMouseMove = (e) => {
      const deltaY = e.clientY - startY
      const newHeight = Math.max(minHeight, Math.min(maxHeight, startHeight + deltaY))
      setEditorHeight(newHeight)
    }

    const handleMouseUp = () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }

    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
  }, [editorHeight, minHeight, maxHeight])

  // 格式化时间
  const formatTime = (date) => {
    if (!date) return ''
    return date.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit',
      second: '2-digit'
    })
  }

  useEffect(() => {
    return () => {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current)
      }
    }
  }, [])

  const editorClasses = `
    prompt-editor
    ${isFullscreen ? 'prompt-editor-fullscreen' : 'relative'}
    ${className}
  `

  const textareaClasses = `
    prompt-editor-textarea w-full px-4 py-3
    ${isFullscreen ? '' : 'border border-gray-300 rounded-lg'}
    text-sm
  `

  return (
    <div className={editorClasses}>
      {/* 工具栏 */}
      {showToolbar && (
        <div className="prompt-editor-toolbar flex items-center justify-between p-3">
          <div className="flex items-center space-x-2">
            {/* 撤销重做 */}
            <div className="flex items-center space-x-1">
              <button
                onClick={handleUndo}
                disabled={historyIndex <= 0}
                className="prompt-editor-btn"
                title="撤销 (Ctrl+Z)"
              >
                <RotateCcw className="h-4 w-4" />
              </button>
              <button
                onClick={handleRedo}
                disabled={historyIndex >= history.length - 1}
                className="prompt-editor-btn"
                title="重做 (Ctrl+Y)"
              >
                <RotateCw className="h-4 w-4" />
              </button>
            </div>

            <div className="w-px h-6 bg-gray-300" />

            {/* 功能按钮 */}
            <button
              onClick={handleSave}
              className="flex items-center space-x-1 px-3 py-1.5 text-sm font-medium text-blue-700 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors"
              title="保存 (Ctrl+S)"
            >
              <Save className="h-4 w-4" />
              <span>保存</span>
            </button>

            <button
              onClick={handleCopy}
              className="prompt-editor-btn"
              title="复制内容"
            >
              <Copy className="h-4 w-4" />
            </button>

            <button
              onClick={() => setShowPreview(!showPreview)}
              className={`prompt-editor-btn ${showPreview ? 'active' : ''}`}
              title="预览模式"
            >
              {showPreview ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            </button>
          </div>

          <div className="flex items-center space-x-2">
            {/* 状态指示 */}
            <div className="flex items-center space-x-2 text-xs text-gray-500">
              {isDirty && (
                <span className="flex items-center space-x-1 text-orange-600">
                  <div className="w-2 h-2 bg-orange-400 rounded-full" />
                  <span>未保存</span>
                </span>
              )}
              {lastSaved && (
                <span className="flex items-center space-x-1">
                  <Clock className="h-3 w-3" />
                  <span>已保存 {formatTime(lastSaved)}</span>
                </span>
              )}
            </div>

            <div className="w-px h-6 bg-gray-300" />

            {/* 统计信息切换 */}
            <button
              onClick={() => setShowStats(!showStats)}
              className="p-2 text-gray-500 hover:text-gray-700 rounded-lg hover:bg-gray-100"
              title="统计信息"
            >
              <Hash className="h-4 w-4" />
            </button>

            {/* 全屏切换 */}
            <button
              onClick={() => setIsFullscreen(!isFullscreen)}
              className="p-2 text-gray-500 hover:text-gray-700 rounded-lg hover:bg-gray-100"
              title="全屏编辑 (F11)"
            >
              {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
            </button>
          </div>
        </div>
      )}

      {/* 编辑区域 */}
      <div className="flex flex-1">
        {/* 主编辑器 */}
        <div className={`flex-1 ${showPreview ? 'w-1/2' : 'w-full'}`}>
          <div className="relative">
            <textarea
              ref={textareaRef}
              value={value}
              onChange={(e) => handleChange(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder={placeholder}
              className={textareaClasses}
              style={{ 
                height: isFullscreen ? 'calc(100vh - 120px)' : `${editorHeight}px`,
                lineHeight: '1.6'
              }}
            />
            
            {/* 拖拽调整手柄 */}
            {!isFullscreen && (
              <div
                ref={resizeHandleRef}
                onMouseDown={handleMouseDown}
                className="absolute bottom-0 left-0 right-0 h-2 cursor-ns-resize bg-gray-100 hover:bg-gray-200 transition-colors flex items-center justify-center"
              >
                <div className="w-8 h-1 bg-gray-400 rounded-full" />
              </div>
            )}
          </div>
        </div>

        {/* 预览区域 */}
        {showPreview && (
          <div className="w-1/2 border-l border-gray-200 bg-gray-50">
            <div className="p-4">
              <h4 className="text-sm font-medium text-gray-700 mb-3">预览</h4>
              <div 
                className="prose prose-sm max-w-none bg-white p-4 rounded-lg border"
                style={{ height: isFullscreen ? 'calc(100vh - 180px)' : `${editorHeight - 60}px`, overflow: 'auto' }}
              >
                <pre className="whitespace-pre-wrap font-mono text-sm text-gray-700 leading-relaxed">
                  {value || '暂无内容...'}
                </pre>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 统计信息栏 */}
      {showStats && (
        <div className="px-4 py-2 bg-gray-50 border-t border-gray-200 text-xs text-gray-600">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <span className="flex items-center space-x-1">
                <Type className="h-3 w-3" />
                <span>{stats.characters} 字符</span>
              </span>
              <span>{stats.words} 词</span>
              <span>{stats.lines} 行</span>
              <span>{stats.paragraphs} 段</span>
            </div>
            <div className="text-gray-500">
              使用 Ctrl+S 保存，Ctrl+Z 撤销，F11 全屏
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default PromptEditor
