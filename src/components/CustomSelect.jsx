import React, { useState, useRef, useEffect } from 'react'
import { ChevronDown, Search, Check } from 'lucide-react'

const CustomSelect = ({
  options = [],
  value,
  onChange,
  placeholder = '请选择...',
  searchable = false,
  disabled = false,
  className = '',
  optionRender,
  valueRender,
  emptyText = '暂无选项',
  searchPlaceholder = '搜索...'
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [focusedIndex, setFocusedIndex] = useState(-1)
  const selectRef = useRef(null)
  const dropdownRef = useRef(null)
  const searchInputRef = useRef(null)

  // 过滤选项
  const filteredOptions = searchable && searchTerm
    ? options.filter(option => 
        option.label?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        option.description?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : options

  // 获取选中的选项
  const selectedOption = options.find(option => option.value === value)

  // 处理点击外部关闭
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (selectRef.current && !selectRef.current.contains(event.target)) {
        setIsOpen(false)
        setSearchTerm('')
        setFocusedIndex(-1)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // 处理键盘导航
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (!isOpen) return

      switch (event.key) {
        case 'ArrowDown':
          event.preventDefault()
          setFocusedIndex(prev => 
            prev < filteredOptions.length - 1 ? prev + 1 : 0
          )
          break
        case 'ArrowUp':
          event.preventDefault()
          setFocusedIndex(prev => 
            prev > 0 ? prev - 1 : filteredOptions.length - 1
          )
          break
        case 'Enter':
          event.preventDefault()
          if (focusedIndex >= 0 && filteredOptions[focusedIndex]) {
            handleSelect(filteredOptions[focusedIndex])
          }
          break
        case 'Escape':
          setIsOpen(false)
          setSearchTerm('')
          setFocusedIndex(-1)
          break
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown)
      return () => document.removeEventListener('keydown', handleKeyDown)
    }
  }, [isOpen, focusedIndex, filteredOptions])

  // 打开下拉框时聚焦搜索框
  useEffect(() => {
    if (isOpen && searchable && searchInputRef.current) {
      setTimeout(() => {
        searchInputRef.current?.focus()
      }, 100)
    }
  }, [isOpen, searchable])

  const handleToggle = () => {
    if (disabled) return
    setIsOpen(!isOpen)
    if (!isOpen) {
      setSearchTerm('')
      setFocusedIndex(-1)
    }
  }

  const handleSelect = (option) => {
    onChange?.(option.value, option)
    setIsOpen(false)
    setSearchTerm('')
    setFocusedIndex(-1)
  }

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value)
    setFocusedIndex(-1)
  }

  return (
    <div ref={selectRef} className={`custom-select ${className}`}>
      {/* Trigger */}
      <div
        className={`custom-select-trigger ${isOpen ? 'open' : ''} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
        onClick={handleToggle}
        tabIndex={disabled ? -1 : 0}
      >
        <div className="flex-1 truncate">
          {selectedOption ? (
            valueRender ? valueRender(selectedOption) : (
              <div className="flex items-center space-x-2">
                {selectedOption.icon && <selectedOption.icon className="h-4 w-4" />}
                <span>{selectedOption.label}</span>
                {selectedOption.badge && (
                  <span className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-full">
                    {selectedOption.badge}
                  </span>
                )}
              </div>
            )
          ) : (
            <span className="text-gray-500">{placeholder}</span>
          )}
        </div>
        <ChevronDown 
          className={`h-4 w-4 text-gray-400 transition-transform duration-200 ${
            isOpen ? 'transform rotate-180' : ''
          }`} 
        />
      </div>

      {/* Dropdown */}
      {isOpen && (
        <div ref={dropdownRef} className="custom-select-dropdown">
          {/* Search */}
          {searchable && (
            <div className="custom-select-search">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  ref={searchInputRef}
                  type="text"
                  value={searchTerm}
                  onChange={handleSearchChange}
                  placeholder={searchPlaceholder}
                  className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          )}

          {/* Options */}
          <div className="max-h-60 overflow-y-auto">
            {filteredOptions.length > 0 ? (
              filteredOptions.map((option, index) => (
                <div
                  key={option.value}
                  className={`custom-select-option ${
                    option.value === value ? 'selected' : ''
                  } ${
                    index === focusedIndex ? 'focused' : ''
                  }`}
                  onClick={() => handleSelect(option)}
                >
                  <div className="flex items-center justify-between w-full">
                    <div className="flex-1">
                      {optionRender ? optionRender(option) : (
                        <div className="flex items-center space-x-2">
                          {option.icon && <option.icon className="h-4 w-4 flex-shrink-0" />}
                          <div className="flex-1 min-w-0">
                            <div className="font-medium text-gray-900 truncate">
                              {option.label}
                            </div>
                            {option.description && (
                              <div className="text-sm text-gray-500 truncate">
                                {option.description}
                              </div>
                            )}
                          </div>
                          {option.badge && (
                            <span className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-full flex-shrink-0">
                              {option.badge}
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                    {option.value === value && (
                      <Check className="h-4 w-4 text-blue-600 flex-shrink-0 ml-2" />
                    )}
                  </div>
                </div>
              ))
            ) : (
              <div className="custom-select-empty">
                {searchTerm ? `没有找到包含"${searchTerm}"的选项` : emptyText}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

export default CustomSelect
