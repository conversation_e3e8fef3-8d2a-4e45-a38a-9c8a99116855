import React from 'react'
import { Navigate, useLocation } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'
import { Loader2 } from 'lucide-react'

const ProtectedRoute = ({ children, requiredPermission }) => {
  const { isAuthenticated, isLoading, hasPermission } = useAuth()
  const location = useLocation()

  console.log('ProtectedRoute check:', { isAuthenticated, isLoading, location: location.pathname })

  // 显示加载状态
  if (isLoading) {
    console.log('ProtectedRoute: showing loading state')
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">正在验证身份...</p>
        </div>
      </div>
    )
  }

  // 未登录，重定向到登录页
  if (!isAuthenticated) {
    console.log('ProtectedRoute: not authenticated, redirecting to login')
    return <Navigate to="/login" state={{ from: location }} replace />
  }

  // 检查权限
  if (requiredPermission && !hasPermission(requiredPermission)) {
    console.log('ProtectedRoute: permission denied for:', requiredPermission)
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">🚫</div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">访问被拒绝</h2>
          <p className="text-gray-600">您没有权限访问此页面</p>
        </div>
      </div>
    )
  }

  console.log('ProtectedRoute: access granted, rendering children')
  return children
}

export default ProtectedRoute
